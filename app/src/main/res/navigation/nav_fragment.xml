<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/nav_fragment"
    app:startDestination="@id/loginFragment">
    <fragment
        android:id="@+id/loginFragment"
        android:name="com.yinduo.compactShelf.view.main.LoginFragment"
        android:label="LoginFragment" >
        <action
            android:id="@+id/action_loginFragment_to_mainFragment"
            app:destination="@id/mainFragment" />
    </fragment>
    <fragment
        android:id="@+id/mainFragment"
        android:name="com.yinduo.compactShelf.view.main.MainFragment"
        android:label="MainFragment" >
        <action
            android:id="@+id/action_mainFragment_to_cleanFragment"
            app:destination="@id/cleanFragment" />
        <action android:id="@+id/action_mainFragment_to_loginFragment"
            app:destination="@+id/loginFragment"/>
        <action
            android:id="@+id/action_mainFragment_to_webViewFragment"
            app:destination="@id/webViewFragment" />
        <action
            android:id="@+id/action_mainFragment_to_geckoViewFragment"
            app:destination="@id/geckoViewFragment" />
        <action
            android:id="@+id/action_mainFragment_to_shelfMotionFragment"
            app:destination="@id/shelfMotionFragment" />
        <action
            android:id="@+id/action_mainFragment_to_localFiveSearchFragment"
            app:destination="@id/localFiveSearchFragment" />

        <action
            android:id="@+id/action_mainFragment_to_localSearchFragment"
            app:destination="@id/localSearchFragment" />

    </fragment>
    <fragment
        android:id="@+id/cleanFragment"
        android:name="com.yinduo.compactShelf.view.main.CleanFragment"
        android:label="CleanFragment" />
    <fragment
        android:id="@+id/webViewFragment"
        android:name="com.yinduo.compactShelf.view.main.WebViewFragment"
        android:label="WebViewFragment" />
    <fragment
        android:id="@+id/geckoViewFragment"
        android:name="com.yinduo.compactShelf.view.main.GeckoViewFragment"
        android:label="GeckoViewFragment" >
        <action
            android:id="@+id/action_geckoViewFragment_to_mainFragment"
            app:destination="@id/mainFragment" />
    </fragment>


    <fragment
        android:id="@+id/localFiveSearchFragment"
        android:name="com.yinduo.compactShelf.view.main.LocalFiveSearchFragment"
        android:label="LocalFiveSearchFragment" >
        <action
            android:id="@+id/action_localFiveSearchFragment_to_shelfMotionFragment"
            app:destination="@id/shelfMotionFragment" />
    </fragment>

    <fragment
        android:id="@+id/localSearchFragment"
        android:name="com.yinduo.compactShelf.view.main.LocalSearchFragment"
        android:label="LocalSearchFragment" >
        <action
            android:id="@+id/action_localSearchFragment_to_shelfMotionFragment"
            app:destination="@id/shelfMotionFragment" />
    </fragment>


    <fragment
        android:id="@+id/screensaverFragment"
        android:name="com.yinduo.compactShelf.view.main.ScreensaverFragment"
        android:label="screensaverFragment">
        <action
            android:id="@+id/action_screensaverFragment_to_loginFragment"
            app:destination="@id/loginFragment" />
        <action
            android:id="@+id/action_screensaverFragment_to_mainFragment"
            app:destination="@id/mainFragment" />
    </fragment>
    <fragment
        android:id="@+id/shelfMotionFragment"
        android:name="com.yinduo.compactShelf.view.main.ShelfMotionFragment"
        android:label="ShelfMotionFragment" >
        <action
            android:id="@+id/action_shelfMotionFragment_to_mainFragment"
            app:destination="@id/mainFragment" />
    </fragment>

</navigation>