package com.yinduo.compactShelf.view.main

import android.Manifest
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.Log
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import androidx.navigation.findNavController
import com.baidu.aip.asrwakeup3.core.mini.AutoCheck
import com.baidu.aip.asrwakeup3.core.recog.IStatus
import com.baidu.aip.asrwakeup3.core.recog.IStatus.STATUS_WAKEUP_SUCCESS
import com.baidu.aip.asrwakeup3.core.recog.MyRecognizer
import com.baidu.aip.asrwakeup3.core.util.AuthUtil
import com.baidu.aip.asrwakeup3.core.util.MyLogger
import com.baidu.aip.asrwakeup3.core.wakeup.MyWakeup
import com.baidu.aip.asrwakeup3.core.wakeup.listener.RecogWakeupListener
import com.baidu.speech.asr.SpeechConstant
import com.italkbb.prime.utils.IRequestPermission
import com.yinduo.compactShelf.BaseApplication.Companion.context
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.base.BaseActivity
import com.yinduo.compactShelf.base.BaseViewModel
import com.yinduo.compactShelf.base.customlivedata.SuperLiveDataManager
import com.yinduo.compactShelf.databinding.ActivityMainBinding
import com.yinduo.compactShelf.room.entity.ScreenSaverConfig
import com.yinduo.compactShelf.room.entity.UserInfo
import com.yinduo.compactShelf.utils.PermissionUtil
import com.yinduo.compactShelf.utils.PlaySoundUtil
import com.yinduo.compactShelf.voice.MyMessageStatusRecogListener
import com.yinduo.compactShelf.voice.OfflineRecogParams
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException

open class MainActivity : BaseActivity<ActivityMainBinding, BaseViewModel>(), IStatus {
    companion object {
        var myRecognizer: MyRecognizer? = null
        var currentUser: UserInfo? = null
    }

    lateinit var context: Context
    private var myWakeup: MyWakeup? = null
    private var screensaverConfig: ScreenSaverConfig? = null
    private val handler = Handler(Looper.getMainLooper())
    private val runnable = Runnable {
        //启动屏保界面
        findNavController(R.id.fragmentContainerView).navigate(R.id.screensaverFragment)

    }
    private lateinit var voiceHandler: myHandler

    override fun getFragmentContentId(): Int {
        return R.id.fl_container
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_main
    }

    override fun getViewModel(): Class<BaseViewModel> {
        return BaseViewModel::class.java
    }

    override fun initView(savedInstanceState: Bundle?) {
    }

    override fun initData() {
        context = this
        voiceHandler = myHandler()
        MyLogger.setHandler(voiceHandler)
        viewModel.screenSaverConfigLiveData.observe(this) {
            screensaverConfig = it
//            if (screensaverConfig != null) {
//                Log.d("test","==========initData====${screensaverConfig!!.startUpTime}==")
//                if (it.startUpTime == 0) {
//                    handler.removeCallbacks(runnable)
//                } else {
//                    handler.postDelayed(runnable, (it.startUpTime * 1000 * 60).toLong())
//                    Log.d("test=====", "initData: ${it.startUpTime}")
//                }
//            }
        }
        PermissionUtil.reqPermission(object : IRequestPermission() {
            override fun accept() {
                viewModel.advancedConfigLiveData.observe(this@MainActivity) {
                    if (it != null && it.isEnableVoice == 0) {
                        startBDVoice()
                    }
                }
            }
        }, Manifest.permission.RECORD_AUDIO)

        SuperLiveDataManager.componentMessage.observeOneOff(this@MainActivity) {
            when (it) {
                "start" -> {
                    //offline engine
                    if (myRecognizer == null) {
                        return@observeOneOff
                    }
                    val offlineParams = OfflineRecogParams.fetchOfflineParams()
                    myRecognizer!!.loadOfflineEngine(offlineParams)
                    val params = AuthUtil.getParam()
                    params.put(SpeechConstant.WP_WORDS_FILE, "assets:///WakeUp.bin")
                    myWakeup!!.start(params)
                }

                "stop" -> {
                    myWakeup?.stop()
                    myRecognizer?.cancel()
                    myRecognizer?.stop()
                }
            }
        }

        SuperLiveDataManager.sleepControl.observeOneOff(this){
            if (it){
                handler.removeCallbacks(runnable)
                handler.post(runnable)
            }else{

            }
        }

    }

    /**
     * wakeup
     * Recog
     * OfflineRecog
     */
    private fun startBDVoice() {
        try {
            val recogListener = MyMessageStatusRecogListener(voiceHandler)
            if(myRecognizer == null){
                myRecognizer = MyRecognizer(this, recogListener)
            }
            //offline engine
            val offlineParams = OfflineRecogParams.fetchOfflineParams()
            myRecognizer!!.loadOfflineEngine(offlineParams)
            //BD voice engine
            val listener = RecogWakeupListener(voiceHandler)
            if (myWakeup == null) {
                myWakeup = MyWakeup(this, listener)
            }

            //start
            val params = AuthUtil.getParam()
            params.put(SpeechConstant.WP_WORDS_FILE, "assets:///WakeUp.bin")
            myWakeup!!.start(params)
        } catch (e: Exception) {
            Log.e("MainActivity", "startBDVoice error: ${e.message}")
        }
    }

    override fun onBackPressed() {

    }

    override fun onPause() {
        super.onPause()
        try {
            when{
                myWakeup != null -> myWakeup?.stop()
                myRecognizer != null -> myRecognizer?.stop()
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "stopBDVoice error: ${e.message}")
        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onDestroy() {
        Log.d("test","==========mainActivity onDestroy===========")
        super.onDestroy()
        myWakeup?.release()
        myRecognizer?.release()
        handler.removeCallbacks(runnable)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        Log.d("test","==========onKeyDown====${keyCode}=${event}=")
        return super.onKeyDown(keyCode, event)
    }
    override fun onUserInteraction() {
        super.onUserInteraction()
        // 用户与应用程序进行交互时重置计时任务
        if (screensaverConfig == null) return
        Log.d("test","==========onUserInteraction====${screensaverConfig!!.startUpTime}==")
        handler.removeCallbacks(runnable)
        if (screensaverConfig != null) {
            if (screensaverConfig!!.startUpTime != 0) {
                handler.postDelayed(
                    runnable,
                    (screensaverConfig!!.startUpTime * 1000 * 60).toLong()
                ) // 重新设置60秒后的执行
            }
        }
    }
    fun startSaverTimer(){
        handler.removeCallbacks(runnable)
        if (screensaverConfig != null) {
            if (screensaverConfig!!.startUpTime != 0) {
                handler.postDelayed(
                    runnable,
                    (screensaverConfig!!.startUpTime * 1000 * 60).toLong()
                ) // 重新设置60秒后的执行
            }
        }
    }
    private class myHandler : Handler() {
        private var backTrackInMs = 0
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                STATUS_WAKEUP_SUCCESS -> {
                    Log.d("MainActivity", "唤醒成功")
                    CoroutineScope(Dispatchers.IO).launch {
                        try {
                            //播放语音
                            val fd = context.assets.openFd(
                                if (MainFragment.voiceType == 0)
                                    "voice_wakeup_ok_female.mp3"
                                else
                                    "voice_wakeup_ok_male.mp3"
                            )
                            PlaySoundUtil.getInstance().playSound(fd)
                            delay(1500)
                            val params = AuthUtil.getParam()
                            params[SpeechConstant.DECODER] = 2
                            params[SpeechConstant.ASR_OFFLINE_ENGINE_GRAMMER_FILE_PATH] =
                                "assets:///baidu_speech_grammar.bsg"
                            params[SpeechConstant.DISABLE_PUNCTUATION] = true
                            params[SpeechConstant.ACCEPT_AUDIO_VOLUME] = false
                            params[SpeechConstant.VAD] = SpeechConstant.VAD_DNN
                            params[SpeechConstant.PID] = 1537
                            if (backTrackInMs > 0) {
                                params[SpeechConstant.AUDIO_MILLS] =
                                    System.currentTimeMillis() - backTrackInMs
                            }
                            withContext(Dispatchers.Main) {
                                AutoCheck(context, object : Handler() {
                                    override fun handleMessage(msg: Message) {
                                        super.handleMessage(msg)
                                        if (msg.what == 100) {
                                            val autoCheck: AutoCheck = msg.obj as AutoCheck
                                            synchronized(autoCheck) {
                                                val message = autoCheck.obtainErrorMessage()
                                                Log.w("AutoCheckMessage", message)
                                            }
                                        }
                                    }
                                }, true).checkAsr(params)
                            }
                            myRecognizer!!.cancel()
                            Log.e("MainActivity", "识别参数:$params")
                            myRecognizer!!.start(params)
                        } catch (e: IOException) {
                            e.printStackTrace()
                        }
                    }
                }

            }
        }
    }

}