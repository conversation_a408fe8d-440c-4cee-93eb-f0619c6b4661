package com.yinduo.compactShelf.view.main

import android.Manifest
import android.util.Log
import android.view.View
import android.view.animation.LinearInterpolator
import android.view.animation.RotateAnimation
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.baidu.idl.main.facesdk.FaceAuth
import com.baidu.idl.main.facesdk.callback.Callback
import com.baidu.idl.main.facesdk.model.BDFaceSDKCommon
import com.example.datalibrary.api.FaceApi
import com.example.datalibrary.callback.FaceDetectCallBack
import com.example.datalibrary.gatecamera.CameraPreviewManager
import com.example.datalibrary.listener.DBLoadListener
import com.example.datalibrary.listener.SdkInitListener
import com.example.datalibrary.manager.FaceSDKManager
import com.example.datalibrary.model.BDFaceCheckConfig
import com.example.datalibrary.model.BDFaceImageConfig
import com.example.datalibrary.model.BDLiveConfig
import com.example.datalibrary.model.LivenessModel
import com.example.datalibrary.model.User
import com.example.datalibrary.utils.FaceOnDrawTexturViewUtil
import com.italkbb.prime.utils.IRequestPermission
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.base.BaseFragment
import com.yinduo.compactShelf.databinding.FragmentLoginFaceBinding
import com.yinduo.compactShelf.log.UserLog
import com.yinduo.compactShelf.manager.SerialPortManager
import com.yinduo.compactShelf.room.repository.UserRepository
import com.yinduo.compactShelf.utils.FaceUtils
import com.yinduo.compactShelf.utils.PermissionUtil
import com.yinduo.compactShelf.utils.SpUtils
import com.yinduo.compactShelf.utils.ToastUtils
import com.yinduo.compactShelf.view.main.bean.LoginBean
import com.yinduo.compactShelf.view.main.bean.SingleBaseConfig
import com.yinduo.compactShelf.view.main.viewModel.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

class FaceLoginFragment: BaseFragment<FragmentLoginFaceBinding, MainViewModel>(), View.OnClickListener  {
    private var isTakePic: Boolean = false
    private var isNavigated = false
    private var shouldSkipLifecycle = false
    private var isStart: Boolean = false
    private var isAuth: Boolean = false
    private var mUser: User? = null
    private lateinit var bdFaceImageConfig: BDFaceImageConfig
    private lateinit var bdLiveConfig: BDLiveConfig
    private lateinit var bdFaceCheckConfig: BDFaceCheckConfig
    val PREVIEW_WIDTH = SingleBaseConfig.getBaseConfig().rgbAndNirWidth
    val PREVIEW_HEIGHT = SingleBaseConfig.getBaseConfig().rgbAndNirHeight
    override fun getLayoutId(): Int {
       return R.layout.fragment_login_face
    }



    override fun getViewModel(): Class<MainViewModel> {
        return MainViewModel::class.java
    }
    override fun initView() {
        binding.click =  this

    }

    override fun initData() {
        shouldSkipLifecycle = true
        binding.glSurface.initSurface(SingleBaseConfig.getBaseConfig().rgbRevert,
            SingleBaseConfig.getBaseConfig().mirrorVideoRGB,false)

        val faceAuth = FaceAuth()
        faceAuth.initLicenseOffLine(requireContext(),object : Callback{
            override fun onResponse(p0: Int, p1: String?) {
                if(p0 == 0){
                    //激活成功
                    isAuth = true
                    initFaceListener()
                    initFaceCheck()
                    FaceApi.getInstance().init(object : DBLoadListener {
                        override fun onStart(successCount: Int) {
                        }

                        override fun onLoad(finishCount: Int, successCount: Int, progress: Float) {
                        }

                        override fun onComplete(features: MutableList<User>?, successCount: Int) {
                            Log.d("test===", "人脸特征数量: ${features?.size}+${features?.get(0)?.userInfo}")
                            FaceApi.getInstance().users = features
                            FaceSDKManager.getInstance().initDataBases(requireContext())
                        }

                        override fun onFail(
                            finishCount: Int,
                            successCount: Int,
                            features: MutableList<User>?
                        ) {
                        }

                    },requireContext())

                }else{
                    runOnUIThread {
                        ToastUtils.showShort("人脸识别激活失败：$p1")
                    }

                }
            }

        })
//        startPreview()
        isTakePic = SpUtils.CACHE.getBoolean("login_pic", false)
    }
    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        Log.d("test","===setUserVisibleHint===$isVisibleToUser===face===")
    }

    private fun initFaceListener() {
        if (FaceSDKManager.initStatus != FaceSDKManager.SDK_MODEL_LOAD_SUCCESS){
            FaceSDKManager.getInstance().initModel(requireContext(),
                FaceUtils.getInstance().bdFaceSDKConfig,object :SdkInitListener{
                override fun initStart() {
                }

                override fun initLicenseSuccess() {
                }

                override fun initLicenseFail(errorCode: Int, msg: String?) {
                }

                override fun initModelSuccess() {
                    FaceSDKManager.initModelSuccess = true
                    Log.d("test","====模型加载成功========$isStart")
                }

                override fun initModelFail(errorCode: Int, msg: String?) {
                    FaceSDKManager.initModelSuccess = false
                    if (errorCode != -12){
                        runOnUIThread {
                            ToastUtils.showShort("模型加载失败，请重新启动")
                        }
                    }

                }

            })
        }
    }
    private fun initFaceCheck() {
        bdFaceCheckConfig = FaceUtils.getInstance().bdFaceCheckConfig
        bdLiveConfig = FaceUtils.getInstance().bdLiveConfig
    }
    private fun initFaceConfig(height: Int, width: Int) {
        bdFaceImageConfig = BDFaceImageConfig(
            height, width,
            SingleBaseConfig.getBaseConfig().rgbDetectDirection,
            SingleBaseConfig.getBaseConfig().mirrorDetectRGB,
            BDFaceSDKCommon.BDFaceImageType.BDFACE_IMAGE_TYPE_YUV_NV21
        )
    }

    override fun onPause() {
        super.onPause()
        Log.d("test","===facelogin  onpause======")
        CameraPreviewManager.getInstance().stopPreview()
        CameraPreviewManager.getInstance().clearmCameraDataCallback()
    }

    override fun onResume() {
        super.onResume()
        Log.d("test","===onresume===face===")
        val rotateAnimation = RotateAnimation(
            0f, 360f,
            RotateAnimation.RELATIVE_TO_SELF, 0.5f,
            RotateAnimation.RELATIVE_TO_SELF, 0.5f,
        )
        rotateAnimation.duration = 2000
        rotateAnimation.fillAfter = true
        rotateAnimation.interpolator = LinearInterpolator()
        rotateAnimation.repeatCount = -1
        binding.ivVerify.startAnimation(rotateAnimation)
        startPreview()

    }
    private fun startPreview(){
        binding.tvVerify.text = "初始化完成"
        PermissionUtil.reqPermission(object :IRequestPermission(){
            override fun accept() {
                    CameraPreviewManager.getInstance().startPreview(binding.glSurface,
                        SingleBaseConfig.getBaseConfig().rgbVideoDirection, PREVIEW_WIDTH, PREVIEW_HEIGHT)
                    viewModel.advancedConfigLiveData.observe(viewLifecycleOwner){
                        val screenType = it?.screenType ?: 0
                        when(screenType){
                            0 -> {
                                CameraPreviewManager.getInstance().cameraFacing =
                                    CameraPreviewManager.CAMERA_FACING_BACK
                            }
                            1 -> {
                                CameraPreviewManager.getInstance().cameraFacing =
                                    CameraPreviewManager.CAMERA_FACING_FRONT
                            }
                            else ->{
                                CameraPreviewManager.getInstance().cameraFacing =
                                    CameraPreviewManager.CAMERA_FACING_BACK
                            }
                        }
                        Log.d("test", "camera direction：${CameraPreviewManager.getInstance().cameraFacing}")
                        val cameraSize = CameraPreviewManager.getInstance().initCamera()
                        Log.d("test", "===camerasize===${cameraSize[1]}===${cameraSize[0]}")
                        initFaceConfig(cameraSize[1], cameraSize[0])
                        Log.d("test","==========isAuth===${isAuth}=========")
                        if(isAuth){
                            CameraPreviewManager.getInstance().setmCameraDataCallback { data, camera, width, height ->
                                binding.glSurface.setFrame()
                                bdFaceImageConfig.setData(data)

                                checkData()
                            }
                        }
                    }
            }

            override fun refuse() {

            }

            override fun noRemind() {
                ToastUtils.showShort("没有相机权限，请前往设置打开")
            }

        },Manifest.permission.CAMERA)
    }

    private fun checkData() {
        Log.d("test","==========checkData=========")
        FaceSDKManager.getInstance().onDetectCheck(bdFaceImageConfig,null,
            null,bdFaceCheckConfig,object :FaceDetectCallBack{
            override fun onFaceDetectCallback(livenessModel: LivenessModel?) {
                checkResult(livenessModel)
            }

            override fun onTip(code: Int, msg: String?) {
            }

            override fun onFaceDetectDarwCallback(livenessModel: LivenessModel?) {
                if (livenessModel == null) {
                    return
                }
                binding.glSurface.onGlDraw(
                    livenessModel.trackFaceInfo,
                    livenessModel.bdFaceImageInstance,
                    FaceOnDrawTexturViewUtil.drawFaceColor(mUser, livenessModel)
                )
            }

        })
    }

    private fun checkResult(livenessModel: LivenessModel?) {
        runOnUIThread {
            if (livenessModel == null){
                ToastUtils.showShort("检测失败")
                return@runOnUIThread
            }
            val user = livenessModel.user
            Log.e("test===", "user: $user" )
            if (user == null){
                mUser = null
                ToastUtils.showShort("识别失败")
                Log.d("TAG", "checkResult: 识别失败")
            }else{
                mUser = user
                ToastUtils.showShort("识别成功")
                Log.d("TAG", "checkResult: 识别成功")
                lifecycleScope.launch(Dispatchers.IO){
                    val userList = UserRepository.getUserByFace(mUser!!.userId)
                    withContext(Dispatchers.Main){
                        if (userList.isEmpty()){
                            ToastUtils.showShort("登录失败")
                        }else{
                            val loginBean = LoginBean()
                            val userInfo = userList[0]
                            loginBean.name = userInfo.loginName.ifEmpty { userInfo.userName }
                            SpUtils.LASTING.putObject("last_login",loginBean)
                            MainActivity.currentUser = userInfo
                            if (isTakePic){
                                val dic = File(requireActivity().getExternalFilesDir(
                                    "user_login")?.absolutePath?:(requireActivity().externalCacheDir?.absolutePath
                                        +"/user_login"))
                                if (!dic.exists()) {
                                    dic.mkdirs()
                                }
                                val path = dic.absolutePath + "/${user.userIndex}-" + System.currentTimeMillis() + ".jpg"
                                val file =  File(path)
                                val stream = FileOutputStream(file)
                                stream.write(bdFaceImageConfig.data)
                                stream.close()
                                lifecycleScope.launch(Dispatchers.IO){
                                    UserLog.logLogin(path)
                                }

                                //登录成功后给移动列上电
                                SerialPortManager.leftPowerOn()
                                SerialPortManager.rightPowerOn()
                                if(!isNavigated) {
                                    findNavController().navigate(R.id.action_loginFragment_to_mainFragment)
                                    isNavigated = true
                                }
                            }else{
                                lifecycleScope.launch(Dispatchers.IO){
                                    UserLog.logLogin()
                                }
                                //登录成功后给移动列上电
                                SerialPortManager.leftPowerOn()
                                SerialPortManager.rightPowerOn()
                                if(!isNavigated) {
                                    findNavController().navigate(R.id.action_loginFragment_to_mainFragment)
                                    isNavigated = true
                                }
                            }
                        }

                    }
                }

            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Log.d("test","===facelogin  onDestroyView======")
        CameraPreviewManager.getInstance().stopPreview()
        CameraPreviewManager.getInstance().clearmCameraDataCallback()
    }
    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        Log.d("test","===facelogin  onhidden===$hidden")
        if (!hidden){
            val rotateAnimation = RotateAnimation(
                0f, 360f,
                RotateAnimation.RELATIVE_TO_SELF, 0.5f,
                RotateAnimation.RELATIVE_TO_SELF, 0.5f,
            )
            rotateAnimation.duration = 2000
            rotateAnimation.fillAfter = true
            rotateAnimation.interpolator = LinearInterpolator()
            rotateAnimation.repeatCount = -1
        }else{

        }
    }

    override fun onClick(v: View) {

    }

}