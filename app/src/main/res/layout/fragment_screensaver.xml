<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.yinduo.compactShelf.view.main.viewModel.ScreenSaverViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_screensaver"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:scaleType="centerCrop"
            android:onClick="@{() -> viewModel.onImageClicked()}"/>
        <TextClock
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="90sp"
            android:textColor="@color/white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_39"
            android:format24Hour="HH:mm"
            android:format12Hour="hh:mm" />
        <TextClock
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_25"
            android:textColor="@color/white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dp_159"
            android:layout_marginStart="@dimen/dp_50"
            android:format24Hour="MM月dd日EE"
            android:format12Hour="MM月dd日EE" />

        <ImageView
            android:layout_width="@dimen/dp_154"
            android:layout_height="@dimen/dp_42"
            android:src="@mipmap/icon_sc_brand"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_40"
            android:visibility="invisible"/>

        <com.example.datalibrary.gl.view.GlMantleSurfacView
            android:id="@+id/gl_surface"
            android:layout_width="1dp"
            android:layout_height="1dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>