package com.yinduo.compactShelf.view.main

import android.Manifest
import android.content.Intent
import android.content.res.Configuration.ORIENTATION_LANDSCAPE
import android.content.res.Configuration.ORIENTATION_PORTRAIT
import android.net.Uri
import android.util.Log
import androidx.core.content.FileProvider
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.baidu.idl.main.facesdk.FaceAuth
import com.baidu.idl.main.facesdk.callback.Callback
import com.baidu.idl.main.facesdk.model.BDFaceSDKCommon
import com.example.datalibrary.api.FaceApi
import com.example.datalibrary.callback.FaceDetectCallBack
import com.example.datalibrary.gatecamera.CameraPreviewManager
import com.example.datalibrary.listener.DBLoadListener
import com.example.datalibrary.listener.SdkInitListener
import com.example.datalibrary.manager.FaceSDKManager
import com.example.datalibrary.model.BDFaceCheckConfig
import com.example.datalibrary.model.BDFaceImageConfig
import com.example.datalibrary.model.BDLiveConfig
import com.example.datalibrary.model.LivenessModel
import com.example.datalibrary.model.User
import com.example.datalibrary.utils.FaceOnDrawTexturViewUtil
import com.italkbb.prime.utils.IRequestPermission
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.base.BaseFragment
import com.yinduo.compactShelf.databinding.FragmentScreensaverBinding
import com.yinduo.compactShelf.log.UserLog
import com.yinduo.compactShelf.manager.SerialPortManager
import com.yinduo.compactShelf.room.entity.ScreenSaverConfig
import com.yinduo.compactShelf.room.entity.SystemConfig
import com.yinduo.compactShelf.room.repository.ScreenSaverConfigRepository
import com.yinduo.compactShelf.room.repository.SystemConfigRepository
import com.yinduo.compactShelf.room.repository.UserRepository
import com.yinduo.compactShelf.utils.FaceUtils
import com.yinduo.compactShelf.utils.PermissionUtil
import com.yinduo.compactShelf.utils.SpUtils
import com.yinduo.compactShelf.utils.ToastUtils
import com.yinduo.compactShelf.view.main.bean.LoginBean
import com.yinduo.compactShelf.view.main.bean.SingleBaseConfig
import com.yinduo.compactShelf.view.main.viewModel.MainViewModel
import com.yinduo.compactShelf.view.main.viewModel.ScreenSaverViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.util.Timer
import java.util.TimerTask

class ScreensaverFragment : BaseFragment<FragmentScreensaverBinding, MainViewModel>() {
    private lateinit var screenSaverViewModel: ScreenSaverViewModel
    private var config: ScreenSaverConfig? = null
    private var imagePaths: MutableList<String> = mutableListOf()
    private var currentIndex = 0
    private var timer: Timer? = null
    private var task: TimerTask? = null
    private var sysConfig: SystemConfig? = null
    private var firstOff = true
    private var isAuth: Boolean = false
    private var isStart: Boolean = false
    private var mUser: User? = null
    private var isTakePic: Boolean = false
    private var faceExit: Boolean = false
    private var isNavigated = false
    private var shouldSkipLifecycle = false
    private lateinit var bdFaceImageConfig: BDFaceImageConfig
    private lateinit var bdLiveConfig: BDLiveConfig
    private lateinit var bdFaceCheckConfig: BDFaceCheckConfig
    val PREVIEW_WIDTH = SingleBaseConfig.getBaseConfig().rgbAndNirWidth
    val PREVIEW_HEIGHT = SingleBaseConfig.getBaseConfig().rgbAndNirHeight
    companion object{
        var isShowing = false
    }
    override fun getLayoutId(): Int {
        return R.layout.fragment_screensaver
    }

    override fun getViewModel(): Class<MainViewModel>? {
        return MainViewModel::class.java
    }

    override fun initView() {
        screenSaverViewModel = ScreenSaverViewModel(findNavController())
        binding.viewModel = screenSaverViewModel
        startSlideshow()
        requireActivity().supportFragmentManager.fragments.forEach { fragment ->
            if (fragment is DialogFragment){
                fragment.dismiss()
            }else{
                findDialogFragment(fragment)
            }
        }
    }
    private fun findDialogFragment(fragment: Fragment){
        fragment.childFragmentManager.fragments.forEach {
            if (it is DialogFragment){
                it.dismiss()
            }else{
                findDialogFragment(it)
            }
        }
    }

    override fun initData() {
        shouldSkipLifecycle = true
        lifecycleScope.launch(Dispatchers.IO){
            sysConfig = SystemConfigRepository.getConfig()
            if((sysConfig?.standby_close_shelf ?: -1) == 1){
                SerialPortManager.openLock()
                SerialPortManager.operateShelf(0x64,0x03)
            }
            withContext(Dispatchers.Main){
                if ((sysConfig?.standby_shutdown_power ?: -1) == 1){
                    SerialPortManager.leftPowerOff()
                    SerialPortManager.rightPowerOff()
                }
            }
        }


        isTakePic = SpUtils.CACHE.getBoolean("login_pic", false)
        faceExit = SpUtils.CACHE.getBoolean("tv_face_exit_saver", false)
        Log.d("test","=====faceExit=========${faceExit}=======")
        if (faceExit){
            binding.glSurface?.initSurface(SingleBaseConfig.getBaseConfig().rgbRevert,
                SingleBaseConfig.getBaseConfig().mirrorVideoRGB,false)

            val faceAuth = FaceAuth()
            faceAuth.initLicenseOffLine(requireContext(),object : Callback {
                override fun onResponse(p0: Int, p1: String?) {
                    Log.d("test","=====initLicenseOffLine=========${p0}==$p1=====")
                    if(p0 == 0){
                        //激活成功
                        isAuth = true
                        initFaceListener()
                        initFaceCheck()
                        FaceApi.getInstance().init(object : DBLoadListener {
                            override fun onStart(successCount: Int) {
                            }

                            override fun onLoad(finishCount: Int, successCount: Int, progress: Float) {
                            }

                            override fun onComplete(features: MutableList<User>?, successCount: Int) {
                                Log.d("test===", "人脸特征数量: ${features?.size}+${features?.get(0)?.userInfo}")
                                FaceApi.getInstance().users = features
                                FaceSDKManager.getInstance().initDataBases(requireContext())
                                Log.d("test","======FaceApi.getInstance======$isStart============")
                                if (!isStart){
                                    runOnUIThread {
                                        startPreview()
                                    }

                                }
                            }

                            override fun onFail(
                                finishCount: Int,
                                successCount: Int,
                                features: MutableList<User>?
                            ) {
                            }

                        },requireContext())

                    }else{
                        runOnUIThread {
                            ToastUtils.showShort("人脸识别激活失败：$p1")
                        }

                    }
                }

            })
            startPreview()
        }
    }
    private fun initFaceListener() {
        if (FaceSDKManager.initStatus != FaceSDKManager.SDK_MODEL_LOAD_SUCCESS){
            FaceSDKManager.getInstance().initModel(requireContext(),
                FaceUtils.getInstance().bdFaceSDKConfig,object : SdkInitListener {
                    override fun initStart() {
                    }

                    override fun initLicenseSuccess() {
                    }

                    override fun initLicenseFail(errorCode: Int, msg: String?) {
                    }

                    override fun initModelSuccess() {
                        FaceSDKManager.initModelSuccess = true
                        Log.d("test","====模型加载成功========")
                    }

                    override fun initModelFail(errorCode: Int, msg: String?) {
                        FaceSDKManager.initModelSuccess = false
                        if (errorCode != -12){
                            runOnUIThread {
                                ToastUtils.showShort("模型加载失败，请重新启动")
                            }
                        }

                    }

                })
        }
    }
    private fun initFaceCheck() {
        bdFaceCheckConfig = FaceUtils.getInstance().bdFaceCheckConfig
        bdLiveConfig = FaceUtils.getInstance().bdLiveConfig
    }
    private fun initFaceConfig(height: Int, width: Int) {
        bdFaceImageConfig = BDFaceImageConfig(
            height, width,
            SingleBaseConfig.getBaseConfig().rgbDetectDirection,
            SingleBaseConfig.getBaseConfig().mirrorDetectRGB,
            BDFaceSDKCommon.BDFaceImageType.BDFACE_IMAGE_TYPE_YUV_NV21
        )
    }
    override fun onPause() {
        super.onPause()
        if (faceExit){
            Log.d("test","===saver  onpause======")
            CameraPreviewManager.getInstance().stopPreview()
            CameraPreviewManager.getInstance().clearmCameraDataCallback()
        }

    }
    override fun onResume() {
        super.onResume()
        Log.d("test","===onresume===saver===")
        if (faceExit){
            startPreview()
        }
        isShowing = true


    }
    override fun onDestroy() {
        super.onDestroy()
        task?.cancel()
        timer?.cancel()
    }
    private fun startPreview(){
        Log.d("test","======startPreview======$isAuth=====$isStart=========")
        if (!isAuth){
            return
        }
        if (isStart){
            return
        }
        Log.d("test","======reqPermission==============")
        PermissionUtil.reqPermission(object : IRequestPermission(){
            override fun accept() {
                Log.d("test","======reqPermission=====accept=========")
                CameraPreviewManager.getInstance().startPreview(binding.glSurface,
                    SingleBaseConfig.getBaseConfig().rgbVideoDirection, PREVIEW_WIDTH, PREVIEW_HEIGHT)
                viewModel.advancedConfigLiveData.observe(viewLifecycleOwner){
                    val screenType = it?.screenType ?: 0
                    when(screenType){
                        0 -> {
                            CameraPreviewManager.getInstance().cameraFacing =
                                CameraPreviewManager.CAMERA_FACING_BACK
                        }
                        1 -> {
                            CameraPreviewManager.getInstance().cameraFacing =
                                CameraPreviewManager.CAMERA_FACING_FRONT
                        }
                        else ->{
                            CameraPreviewManager.getInstance().cameraFacing =
                                CameraPreviewManager.CAMERA_FACING_BACK
                        }
                    }
                    Log.d("test", "camera direction：${CameraPreviewManager.getInstance().cameraFacing}")
                    val cameraSize = CameraPreviewManager.getInstance().initCamera()
                    Log.d("test", "===camerasize===${cameraSize[1]}===${cameraSize[0]}")
                    initFaceConfig(cameraSize[1], cameraSize[0])
                    Log.d("test","==========isAuth===${isAuth}=========")
                    if(isAuth){
                        isStart = true
                        CameraPreviewManager.getInstance().setmCameraDataCallback { data, camera, width, height ->
                            binding.glSurface?.setFrame()
                            bdFaceImageConfig.setData(data)

                            checkData()
                        }
                    }
                }
            }

            override fun refuse() {
                Log.d("test","======reqPermission=====refuse=========")
            }

            override fun noRemind() {
                Log.d("test","======reqPermission=====noRemind=========")
                ToastUtils.showShort("没有相机权限，请前往设置打开")
            }

        }, Manifest.permission.CAMERA)
    }

    private fun checkData() {
//        Log.d("test","===saver  checkData======")
        FaceSDKManager.getInstance().onDetectCheck(bdFaceImageConfig,null,
            null,bdFaceCheckConfig,object : FaceDetectCallBack {
                override fun onFaceDetectCallback(livenessModel: LivenessModel?) {
                    checkResult(livenessModel)
                }

                override fun onTip(code: Int, msg: String?) {
                }

                override fun onFaceDetectDarwCallback(livenessModel: LivenessModel?) {
                    if (livenessModel == null) {
                        return
                    }
                    binding.glSurface?.onGlDraw(
                        livenessModel.trackFaceInfo,
                        livenessModel.bdFaceImageInstance,
                        FaceOnDrawTexturViewUtil.drawFaceColor(mUser, livenessModel)
                    )
                }

            })
    }

    private fun checkResult(livenessModel: LivenessModel?) {
        runOnUIThread {
            if (livenessModel == null){
//                ToastUtils.showShort("检测失败")
                return@runOnUIThread
            }
            val user = livenessModel.user
            Log.e("test===", "user: $user" )
            if (user == null){
                mUser = null
                ToastUtils.showShort("识别失败")
                Log.d("TAG", "checkResult: 识别失败")
            }else{
                mUser = user
                ToastUtils.showShort("识别成功")
                Log.d("TAG", "checkResult: 识别成功")
                lifecycleScope.launch(Dispatchers.IO){
                    val userList = UserRepository.getUserByFace(mUser!!.userId)
                    withContext(Dispatchers.Main){
                        if (userList.isEmpty()){
                            ToastUtils.showShort("登录失败")
                        }else{
                            val loginBean = LoginBean()
                            val userInfo = userList[0]
                            loginBean.name = userInfo.loginName.ifEmpty { userInfo.userName }
                            SpUtils.LASTING.putObject("last_login",loginBean)
                            MainActivity.currentUser = userInfo
                            if (isTakePic){
                                val dic = File(requireActivity().getExternalFilesDir(
                                    "user_login")?.absolutePath?:(requireActivity().externalCacheDir?.absolutePath
                                        +"/user_login"))
                                if (!dic.exists()) {
                                    dic.mkdirs()
                                }
                                val path = dic.absolutePath + "/${user.userIndex}-" + System.currentTimeMillis() + ".jpg"
                                val file =  File(path)
                                val stream = FileOutputStream(file)
                                stream.write(bdFaceImageConfig.data)
                                stream.close()
                                lifecycleScope.launch(Dispatchers.IO){
                                    UserLog.logLogin(path)
                                }

                                //登录成功后给移动列上电
                                SerialPortManager.leftPowerOn()
                                SerialPortManager.rightPowerOn()
                                if(!isNavigated) {
                                    findNavController().navigate(R.id.action_screensaverFragment_to_mainFragment)
                                    isNavigated = true
                                }
                            }else{
                                lifecycleScope.launch(Dispatchers.IO){
                                    UserLog.logLogin()
                                }
                                //登录成功后给移动列上电
                                SerialPortManager.leftPowerOn()
                                SerialPortManager.rightPowerOn()
                                if(!isNavigated) {
                                    findNavController().navigate(R.id.action_screensaverFragment_to_mainFragment)
                                    isNavigated = true
                                }
                            }

                        }

                    }
                }

            }
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        Log.d("test","===saver  onDestroyView======")
        if (faceExit){
            CameraPreviewManager.getInstance().stopPreview()
            CameraPreviewManager.getInstance().clearmCameraDataCallback()
        }
        isShowing = false
    }
    private fun startSlideshow() {
        lifecycleScope.launch(Dispatchers.IO) {
            config = ScreenSaverConfigRepository.getConfig()

            if (config != null && config!!.picPath.size>0) {
                imagePaths.clear()
                imagePaths = config!!.picPath
                timer = Timer()
                task = object : TimerTask() {
                    override fun run() {
                        currentIndex = (currentIndex + 1) % imagePaths.size
                        lifecycleScope.launch(Dispatchers.Main) {
                            try {
                                val imagePath = imagePaths[currentIndex]
                                Log.d("ScreensaverFragment", "Loading image path: $imagePath")

                                // Use Uri.parse for all types of paths to avoid FileProvider permission issues
                                val uri = Uri.parse(imagePath)
                                binding.ivScreensaver.setImageURI(uri)
                                Log.d("ScreensaverFragment", "Successfully loaded image: $imagePath")
                            } catch (e: Exception) {
                                Log.e("ScreensaverFragment", "Error loading image: ${e.message}")
                                // Try alternative approach if Uri.parse fails
                                try {
                                    val imagePath = imagePaths[currentIndex]
                                    if (imagePath.startsWith("/")) {
                                        // For file paths, try Uri.fromFile
                                        val file = File(imagePath)
                                        if (file.exists()) {
                                            binding.ivScreensaver.setImageURI(Uri.fromFile(file))
                                            Log.d("ScreensaverFragment", "Loaded with Uri.fromFile: $imagePath")
                                        }
                                    }
                                } catch (fallbackException: Exception) {
                                    Log.e("ScreensaverFragment", "All loading methods failed: ${fallbackException.message}")
                                }
                            }
                        }
                    }
                }
                timer?.scheduleAtFixedRate(task, 0, ((config?.picInterval ?: 5) * 1000).toLong())
            } else {
                lifecycleScope.launch(Dispatchers.Main) {
                    val orientation = resources.configuration.orientation
                    Log.d("screensaver", "orientation:${orientation}")
                    if (orientation == ORIENTATION_LANDSCAPE) {
                        binding.ivScreensaver.setImageResource(R.mipmap.bg_screensaver1)
                    } else if (orientation == ORIENTATION_PORTRAIT) {
                        binding.ivScreensaver.setImageResource(R.mipmap.bg_screensaver_v)
                    }
                }
            }
        }


    }
}