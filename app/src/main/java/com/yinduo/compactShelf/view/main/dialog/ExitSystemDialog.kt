package com.yinduo.compactShelf.view.main.dialog

import android.content.ComponentName
import android.content.Intent
import android.content.pm.ComponentInfo
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import com.yinduo.compactShelf.BaseApplication
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.databinding.DialogExitSystemBinding
import com.yinduo.compactShelf.room.repository.AdvancedConfigRepository
import com.yinduo.compactShelf.utils.AppManager
import com.yinduo.compactShelf.utils.DeviceUtil
import com.yinduo.compactShelf.view.main.MainActivity
import com.ys.rkapi.MyManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.system.exitProcess

class ExitSystemDialog() : DialogFragment(), View.OnClickListener {
    private lateinit var binding: DialogExitSystemBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, R.style.dialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.dialog_exit_system,
            null,
            false
        )
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.click = this
    }

    override fun onResume() {
        super.onResume()
        val window = dialog!!.window
        window!!.setGravity(Gravity.CENTER)
        dialog!!.window!!.decorView.setPadding(DeviceUtil.dip2px(20f), 0, DeviceUtil.dip2px(20f), 0)
        dialog!!.window!!.setLayout(DeviceUtil.dip2px(348f), DeviceUtil.dip2px(150.5f))
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.bt_complete -> {
//                CoroutineScope(Dispatchers.IO).launch {
//                    val advanceConfig = AdvancedConfigRepository.getConfig()
//                    when(advanceConfig?.screenType){
//                        0 ->{
//                            val manager = MyManager.getInstance(BaseApplication.context)
//                            manager.hideStatusBar(true)
//                            exitSystem()
//                        }
//                        2 ->{
////                            val myService = MyService(BaseApplication.context)
////                            myService.hideNavBar = false
//                            exitSystem()
//                        }
//                    }
//                }
                exitSystem()
            }

            R.id.bt_cancel -> {
                dismiss()
            }

            R.id.iv_close -> {
                dismiss()
            }
        }
    }

    fun exitSystem(){
//                    exitProcess(1)
        Log.d(" test","=======退出app========")
//        AppManager.appManager.AppExit(requireContext())
//        val intent = Intent(Intent.ACTION_MAIN)
//        intent.addCategory(Intent.CATEGORY_HOME)
        val queryLauncher = Intent(Intent.ACTION_MAIN)
        queryLauncher.addCategory(Intent.CATEGORY_HOME)
        val packageManager = requireActivity().packageManager
        val apps =  packageManager.queryIntentActivities(queryLauncher,0)
        apps.forEach { launcher->
            val packageName = launcher.activityInfo.packageName
            Log.d("test","===========launcher 包名===$packageName===========")
            if (packageName != "com.yinduo.compactShelf"){
                Log.d("test","=======启动这个==========")
                val intent = Intent()
//                intent.setPackage(packageName)
                intent.action = Intent.ACTION_MAIN
                intent.addCategory(Intent.CATEGORY_HOME)
                val resolveInfo = packageManager.resolveActivity(intent,PackageManager.MATCH_DEFAULT_ONLY)
                if (resolveInfo == null){
                    Log.d("test","============未查找到启动activity==============")
                }else{
                    val componet = ComponentName(resolveInfo!!.activityInfo.packageName,resolveInfo!!.activityInfo.name)
                    intent.setComponent(componet)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    requireActivity().startActivity(intent)
                    requireActivity().finish()
                    return
                }

            }
        }
    }
}