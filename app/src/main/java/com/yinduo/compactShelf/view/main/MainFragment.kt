package com.yinduo.compactShelf.view.main

import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.AnimationDrawable
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.View.OnClickListener
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.google.gson.Gson
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.background.workers.CleanDataSaveWorker
import com.yinduo.compactShelf.base.BaseFragment
import com.yinduo.compactShelf.base.customlivedata.SuperLiveDataManager
import com.yinduo.compactShelf.base.customlivedata.base.postOneOff
import com.yinduo.compactShelf.databinding.FragmentMainBinding
import com.yinduo.compactShelf.manager.SerialPortManager
import com.yinduo.compactShelf.manager.voice.VoiceSerialPortManager
import com.yinduo.compactShelf.room.entity.AdvancedConfig
import com.yinduo.compactShelf.room.entity.SystemConfig
import com.yinduo.compactShelf.room.entity.UserInfo
import com.yinduo.compactShelf.room.repository.AdvancedConfigRepository
import com.yinduo.compactShelf.room.repository.UserRepository
import com.yinduo.compactShelf.server.ServerManager
import com.yinduo.compactShelf.server.websocket.WebsocketManager
import com.yinduo.compactShelf.utils.DeviceUtil
import com.yinduo.compactShelf.utils.PlaySoundUtil
import com.yinduo.compactShelf.utils.SpUtils
import com.yinduo.compactShelf.utils.TTSUtils
import com.yinduo.compactShelf.utils.ToastUtils
import com.yinduo.compactShelf.utils.dtoast.showShortToast
import com.yinduo.compactShelf.utils.dtoast.showToast
import com.yinduo.compactShelf.view.main.adapter.ShelfPagerAdapter
import com.yinduo.compactShelf.view.main.bean.SearchConfigBean
import com.yinduo.compactShelf.view.main.bean.ShelfBean
import com.yinduo.compactShelf.view.main.bean.ShelfSearchBodyBean
import com.yinduo.compactShelf.view.main.bean.ShelfSearchResultBean
import com.yinduo.compactShelf.view.main.dialog.LocateLightManualDialog
import com.yinduo.compactShelf.view.main.dialog.MoreDialog
import com.yinduo.compactShelf.view.main.dialog.PasswordDialog
import com.yinduo.compactShelf.view.main.dialog.QueueDialog
import com.yinduo.compactShelf.view.main.dialog.SearchConfigDialog
import com.yinduo.compactShelf.view.main.viewModel.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import java.util.concurrent.TimeUnit
import kotlin.math.abs


class MainFragment : BaseFragment<FragmentMainBinding, MainViewModel>(),OnClickListener {
    private var shelfSearchResult: List<ShelfSearchResultBean> = listOf()
    private var currentSearchIndex: Int = 0 // 当前给移动列下发的搜索条目index
    private var searchKey: String = ""
    private var isShelfSearch: Boolean = false
    private var remoteAddress: String = ""
    private var isClickSearch: Boolean = false
    private var isMusicPlayed: Boolean = false
    private var isUnlockMscPlayed: Boolean = false
    private var startX: Float = 0.toFloat()
    private var startY: Float = 0.toFloat()
    private var currentLockStatus = 0
    private var lastLockStatus = -1
    private var currentViewId: Int = 0
    private var isFirst: Boolean = true
    private lateinit var checkPasswordDialog: PasswordDialog
    private lateinit var setPasswordDialog: PasswordDialog
    private var currentShelf: ShelfBean? = null
    private var currentPage: Int = 0
    private var currentSelectPage: Int = 0
    private var allPage = 0 //总页数
    private var stickPage = 0 //固定列所在页数
    private var config: SystemConfig? = null
    private val pageList = mutableListOf<List<ShelfBean>>()
    private lateinit var shelfAdapter: ShelfPagerAdapter
    private lateinit var advancedConfig: AdvancedConfig

    companion object{
        fun newInstance(): MainFragment {
            return MainFragment()
        }
        var voiceType = 0 // 0女声  1男声
        var BaseUrl = ""
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_main
    }

    override fun getViewModel(): Class<MainViewModel> {
        return MainViewModel::class.java
    }

    override fun initView() {
        binding.click = this
        lifecycleScope.launch(Dispatchers.IO){
            advancedConfig = AdvancedConfigRepository.getConfig() ?: AdvancedConfig()
            Log.d("test", "${advancedConfig.molPicPath} ------------ ${advancedConfig.fixPicPath}")
            if (advancedConfig.fixPicPath.isNotEmpty() && advancedConfig.molPicPath.isNotEmpty()) {
                withContext(Dispatchers.Main) {
                    binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                    binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                    binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                }
            }
        }
        shelfAdapter = ShelfPagerAdapter(requireContext()){
            //需要更改上次选择的密集架状态和本次选择的状态
            if (currentShelf != null){
                currentShelf!!.isSelected = false
                shelfAdapter.notifyItemChanged(currentSelectPage)
            }
            currentShelf = shelfAdapter.getItem(currentPage)[it]
            currentShelf!!.isSelected =  true
            currentSelectPage = currentPage
            shelfAdapter.notifyItemChanged(currentSelectPage)
            lifecycleScope.launch(Dispatchers.IO){
                Log.d(TAG, "${advancedConfig.molPicPath} ------------ ${advancedConfig.fixPicPath}")
                if (advancedConfig.fixPicPath.isNotEmpty() && advancedConfig.molPicPath.isNotEmpty()){
                    withContext(Dispatchers.Main){
                        if (currentShelf!!.address < 0x64){
                            binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                            binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                            binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                        }else if (currentShelf!!.address == (0x64).toByte()){
                            binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                            binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                            binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                        }else{
                            binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                            binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                            binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                        }
                    }
                }else {
                    withContext(Dispatchers.Main){
                        if (currentShelf!!.address < 0x64){
                            binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_move_yd)
                            binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_move_yd)
                            binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_regular_yd)
                        }else if (currentShelf!!.address == (0x64).toByte()){
                            binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_regular_yd)
                            binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_move_yd)
                            binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_move_yd)
                        }else{
                            binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_move_yd)
                            binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_regular_yd)
                            binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_move_yd)
                        }
                    }
                }
            }
            VoiceSerialPortManager.setShelf(currentShelf!!)
            showShortToast("当前选择：第${currentPage}页 第${currentShelf!!.order}个 地址：${currentShelf!!.address}")
//            getShelfStatus()
        }
        binding.vpShelfList.adapter = shelfAdapter
        binding.vpShelfList.isUserInputEnabled = false

        binding.vTouchView.setOnTouchListener { _, event ->handleTouchEvent(event)  }
    }

    private fun handleTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                startX = event.x
                startY = event.y
            }
            MotionEvent.ACTION_UP -> {
                if (MainActivity.currentUser != null &&
                    MainActivity.currentUser!!.permission_shelf == 0){
                    showShortToast("无权限操作请联系管理员")
                    return true
                }
                val endX = event.x
                val endY = event.y
                val deltaX = endX - startX
                val deltaY = endY - startY
                if (abs(deltaX) > abs(deltaY)) {
                    // 水平滑动
                    if (deltaX > 0) {
                        // 向右滑动
                        handleSwipeRight()
                    } else {
                        // 向左滑动
                        handleSwipeLeft()
                    }
                }
            }
        }
        return true
    }

    private fun handleSwipeLeft() {
        moveShelfLeft()
    }

    private fun handleSwipeRight() {
        moveShelfRight()
    }

    private fun getShelfStatus() {
        SerialPortManager.getLockStatus(currentShelf!!.address)
    }

    override fun onResume() {
        super.onResume()
        startAnimation()
        Log.d("test","====resume newconfig===${requireActivity().resources.configuration.densityDpi}===" +
                "\n===density===${requireActivity().resources.displayMetrics.density}===" +
                "\n===densityDpi===${requireActivity().resources.displayMetrics.densityDpi}===" +
                "\n===widthPixels===${requireActivity().resources.displayMetrics.widthPixels}===" +
                "\n===heightPixels===${requireActivity().resources.displayMetrics.heightPixels}===" +
                "\n===scaledDensity===${requireActivity().resources.displayMetrics.scaledDensity}===")
        (requireActivity() as MainActivity).startSaverTimer()
    }

    override fun onPause() {
        super.onPause()
        stopAnimation()
    }
    private fun startAnimation() {
        (binding.ivSafeStatus.background as AnimationDrawable).start()
        (binding.ivLightOutLeft.background as AnimationDrawable).start()
        (binding.ivLightOutRight.background as AnimationDrawable).start()
        (binding.ivLightInnerLeft.background as AnimationDrawable).start()
        (binding.ivLightInnerRight.background as AnimationDrawable).start()
    }
    private fun stopAnimation() {
        (binding.ivSafeStatus.background as AnimationDrawable).stop()
        (binding.ivLightOutLeft.background as AnimationDrawable).stop()
        (binding.ivLightOutRight.background as AnimationDrawable).stop()
        (binding.ivLightInnerLeft.background as AnimationDrawable).stop()
        (binding.ivLightInnerRight.background as AnimationDrawable).stop()
    }

    override fun initData() {
//        try {
//            //播放语音
//            val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
//                "shelf_power_open_female.mp3"
//            else
//                "shelf_power_open_male.mp3")
//            PlaySoundUtil.getInstance().playSound(fd)
//        } catch (e: IOException) {
//            e.printStackTrace()
//        }

        viewModel.airLiveData.observe(viewLifecycleOwner){
            if (SerialPortManager.isShowLog){
                Log.d("test","====air数据库有更新=====${it?.temp}===${it?.rh}")
            }
            it?.apply {
                if (it.temp != null){
                    binding.tvTemp.text = temp + "℃"
                }
                if (it.rh != null){
                    binding.tvRh.text = rh + "%RH"
                }
            }
        }

        //配置信息监听
        viewModel.configLiveData.observe(viewLifecycleOwner){
            if (it == null){
                Log.d("test","====config  null=====")
                showShortToast("没有找到有效配置信息，请进行参数配置")
                return@observe
            }
            if(it.area_number == 0 || it.column_order ==0){
                showShortToast("本区列数或本列列号为0，请重新配置")
                return@observe
            }
            if (it.area_number > 0 && it.column_order > 0){
                config = it
                //更改除尘 图标
                when(it.clean_name){
                    0->{
                        binding.btSetting.background = ContextCompat.getDrawable(requireContext(),R.drawable.selector_bt_controller_clean)
                    }
                    1->{
                        binding.btSetting.background = ContextCompat.getDrawable(requireContext(),R.drawable.selector_bt_controller_chusuan)
                    }
                    2->{
                        binding.btSetting.background = ContextCompat.getDrawable(requireContext(),R.drawable.selector_bt_controller_cusuan)
                    }
                }

                if (SerialPortManager.DEFAULT_SERIALPATH != it.serial_port
                    || SerialPortManager.DEFUALT_BAUDRATE != it.baud_rate){
                    SerialPortManager.stopRun()
                    SerialPortManager.startRun(it.serial_port, it.baud_rate, it.column_order == 1)
                    // 发送传感器类型
                    SerialPortManager.writeSensorType(if (config!!.sensor_type == 1) 0x01 else 0x00)
                }
                //男女声音
                voiceType = config?.voice?:0
                //接口baseUrl
                BaseUrl = "http://"+it.service_address+":"+it.service_port
                //启动服务器
                if (isFirst){
                    ServerManager.setServer(requireContext(), it.service_port)
                    isFirst = false
                    WebsocketManager.startServer(it.service_port)
                }else{
                    if (ServerManager.port != it.service_port){
                        ServerManager.stopServer()
                        Log.d("test", "initData: 端口改变：${it.service_port}" )
                        ServerManager.setServer(requireContext(), it.service_port)
                    }
                }

                ServerManager.startServer()
                //默认选择固定列
                try {
                    allPage = if (it.area_number % 6 ==0) it.area_number/6 else (it.area_number/6)+1
                    stickPage = if (it.column_order % 6 ==0) it.column_order/6 else (it.column_order/6)+1
                    Log.d("test","===allpage=$allPage===areaNumber=${it.area_number}===columnOrder=${it.column_order}==stickPage=${stickPage}")
                    //填充每页数据
                    pageList.clear()
                    for (i in 1 .. allPage){
                        val currentPage = mutableListOf<ShelfBean>()
                        for (j in 1 .. 6){
                            val index = (i-1)*6 + j
                            if (index <= it.area_number){
                                currentPage.add(ShelfBean((0x64+(index - it.column_order)).toByte(), index == it.column_order, index,index == it.column_order))
                            }
                        }
                        pageList.add(currentPage)
                    }
                    Log.d("test","===pageList===${pageList.size}")
                    currentShelf = pageList[stickPage-1][(it.column_order-1) % 6]
                    currentPage = 0
                    currentSelectPage = 0
                    shelfAdapter.items = pageList
                    binding.vpShelfList.currentItem = 0
                }catch (e:Exception){
                    e.printStackTrace()
                    showToast("参数配置错误 请重新配置")
                }

                if (it.service_address.isNotEmpty()){
                    viewModel.queryArchiveTotal(config?.device_ip?:(DeviceUtil.iP?:""))
                }
                val searchConfigString = SpUtils.LASTING.getString(SearchConfigDialog.SEARCH_CONFIG_KEY,"")
                val searchConfig = if (searchConfigString.isNullOrEmpty()){
                    SearchConfigBean()
                }else{
                    Gson().fromJson(searchConfigString, SearchConfigBean::class.java)
                }
                if (searchConfig.isRemote && it.scan_enable == 1){
                    binding.tvScan.visibility = View.VISIBLE
                    binding.tvTask.visibility = View.VISIBLE
                }else{
                    binding.tvScan.visibility = View.GONE
                    binding.tvTask.visibility = View.GONE
                }
//                getShelfStatus()
            }
        }
        refreshPieChart(800,0,0,"0")
        //档案数据
        viewModel.archiveNumLiveData.observe(this){
            val nums = it.split("-")
            refreshPieChart(nums[0].toInt(),nums[1].toInt(),nums[2].toInt(),nums[3])
            binding.tvArchTotalNum.text = "${nums[0]}   盒"
            binding.tvArchInResponseNum.text =  "${nums[1]}   盒"
            binding.tvArchBorrowNum.text =  "${nums[2]}   盒"
//            binding.tvResponseCapacityNum.text = "${nums[3]}   盒"
        }

        SuperLiveDataManager.dialogName.observeOneOff(viewLifecycleOwner){
            activity?.let { it1 -> it.show(it1.supportFragmentManager, "open_from_interface") }
        }

        //锁定状态
        SuperLiveDataManager.getLockStatus.observeOneOff(viewLifecycleOwner){
            currentLockStatus = it
            binding.btRelease.isSelected = it != 0
            when(it){
                0->{
                    binding.tvShelfStatus.setText("解锁")
                    if(isMusicPlayed){
                        isMusicPlayed = false
//                        PlaySoundUtil.getInstance().stop()
                    }
                    when(isUnlockMscPlayed){
                        true -> return@observeOneOff
                        false -> {
                            try {
                                isUnlockMscPlayed = true
                                //播放语音
                                val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
                                    "release_lock_female.mp3"
                                else
                                    "release_lock_open_male.mp3")
                                PlaySoundUtil.getInstance().playSound(fd)
                            } catch (e: IOException) {
                                e.printStackTrace()
                            }
                        }
                    }
                }
                1->{
                    binding.tvShelfStatus.setText("手动锁定")
                    when(isMusicPlayed){
                        true -> return@observeOneOff
                        false -> {
                            try {
                                isUnlockMscPlayed = false
                                //播放语音
                                val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
                                    "shelf_lock_female.mp3"
                                else
                                    "shelf_lock_male.mp3")
                                PlaySoundUtil.getInstance().playSound(fd)
                                isMusicPlayed = true
                            } catch (e: IOException) {
                                e.printStackTrace()
                            }
                        }
                    }
                }
                2->{
                    binding.tvShelfStatus.setText("通道内有人或物")
                        if(isMusicPlayed) return@observeOneOff
                        if(!isMusicPlayed){
                            try {
                                isUnlockMscPlayed = false
                                //播放语音
                                val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
                                    "shelf_stop_have_something_female.mp3"
                                else
                                    "shelf_stop_have_something_male.mp3")
                                PlaySoundUtil.getInstance().playSound(fd)
                                isMusicPlayed = true
                            } catch (e: IOException) {
                                e.printStackTrace()
                            }
                        }
                    showBreathLight()
                }


                3->{
                    binding.tvShelfStatus.setText("移动时间过长")
                    when{
                        isUnlockMscPlayed -> isUnlockMscPlayed = false
                    }
                    showBreathLight()
                }
                4->{
                    binding.tvShelfStatus.setText("移动距离过长")
                    when{
                        isUnlockMscPlayed -> isUnlockMscPlayed = false
                    }
                    showBreathLight()
                }
                5->{
                    binding.tvShelfStatus.setText("移动阻力过大（过流）")
                    when{
                        isUnlockMscPlayed -> isUnlockMscPlayed = false
                    }
                    showBreathLight()
                }
            }
            lastLockStatus = it
        }
        //打开队列弹窗
        SuperLiveDataManager.openQueueOrder.observeOneOff(viewLifecycleOwner){
            val dialog = QueueDialog()
            dialog.show(parentFragmentManager,"queue_order")
        }
        SuperLiveDataManager.shelfMotionState.observeOneOff(requireActivity()){
            Log.d("test","======main fragment shelfMotionState====${it}==========")
            when(it){
                1,
                2,
                3,
                4,
                5->{
                    if (findNavController().currentDestination?.id == R.id.mainFragment){
                        findNavController().navigate(R.id.action_mainFragment_to_shelfMotionFragment)
                    }
                    if (SerialPortManager.sendOperateOrder){
                        SerialPortManager.sendOperateOrder = false

                        if (it == 1){
                            when(SerialPortManager.operateType){
                                1->{
                                    val index = (SerialPortManager.operateAddress-0x64)+config!!.column_order
                                    val page = if (index%6 == 0) index/6 else index/6 +1
                                    val pageIndex = if (index%6 == 0) page*6-1  else index-(page-1)*6-1
                                    currentPage = page
                                    binding.vpShelfList.currentItem = currentPage
                                    shelfAdapter.setShelfDivider(page-1,pageIndex,true,config!!.column_order > index)
                                }

                                2,3->{
                                    shelfAdapter.setShelfDivider(-1,-1,false,false)
                                }
                            }


                        }else{
                            Log.d("test","=====开架异常=====")
                        }
                    }

                }
            }
        }
        if(isFirst){
            scheduleSaveDataTask()
        }
        viewModel.advancedConfigLiveData.observe(viewLifecycleOwner){
            if ((it?.isEnableVoice ?: -1) == 1){
                SuperLiveDataManager.componentMessage.postOneOff("stop")
                if (VoiceSerialPortManager.DEFAULT_SERIALPATH != it.voicePort){
                    VoiceSerialPortManager.stopRun()
                    VoiceSerialPortManager.startRun(it.voicePort, 9600)
                }
            }else{
                VoiceSerialPortManager.stopRun()
            }
            SpUtils.LOCATElIGHT.putInt("locate_light_type", it?.locateLightType ?: 0)
            SpUtils.LOCATElIGHT.putInt("locate_light_enable", it?.isEnableLocateLight ?: 0)
        }
        TTSUtils.initEngine(requireContext())
        SuperLiveDataManager.locateLightState.observeOneOff(viewLifecycleOwner){
            when(it){
                1 ->{
                    if(SpUtils.LOCATElIGHT.getInt("locate_light_type") == 1
                        && SpUtils.LOCATElIGHT.getInt("locate_light_enable") == 1){
                        val dialog = LocateLightManualDialog()
                        activity?.let { it1 -> dialog.show(it1.supportFragmentManager, "locate_light_manual_dialog") }
                    }
                }
            }
        }
        SuperLiveDataManager.molSearchText.observeOneOff(viewLifecycleOwner){
//            if(it.isNotEmpty() && it.isNotBlank()){
//                val bundle = Bundle()
//                bundle.putString("searchKeyWord", it)
//                findNavController().navigate(R.id.localFiveSearchFragment, bundle)
//            }
            //不打开本地搜索  直接接口搜
            Log.d("test","====移动列搜索触发====${it}")
            if (!it.isNullOrEmpty()){
                isShelfSearch = true
                searchKey = it
                viewModel.loginFive(1)
            }


        }

        //第五版搜索登录成功
        viewModel.loginSuccess.observeOneOff(viewLifecycleOwner){
            val token = it.keys.iterator().next()
            val type = it[token]
            if (isClickSearch){
                isClickSearch = false
            }
            if (isShelfSearch){
                //移动列搜索
                isShelfSearch = false
                //档案盒数据查询
                viewModel.shelfSearch(ShelfSearchBodyBean(1,100,searchKey))
                searchKey = ""
            }else{
                val bundle = Bundle()
                bundle.putString("url", "$remoteAddress?token=$it")
                bundle.putInt("type", type?:1)
                Log.d("test","===========登陆成功  跳转type $type===============")
                findNavController().navigate(
                    R.id.action_mainFragment_to_geckoViewFragment,
                    bundle
                )
            }

        }
        //移动列搜索获取结果
        viewModel.shelfSearchList.observeOneOff(this) {
            shelfSearchResult = it
            currentSearchIndex = 0
            //给移动列下发第一条结果
            SerialPortManager.writeSearchResult(shelfSearchResult[currentSearchIndex],shelfSearchResult.size,1)
        }
        SuperLiveDataManager.shelfSearchOprate.observeOneOff(viewLifecycleOwner){
            if (shelfSearchResult.isNullOrEmpty()){
                Log.d("test","======搜索结果为空，无法操作======")
                return@observeOneOff
            }
            when(it){
                1->{
                    if (currentSearchIndex < shelfSearchResult.size -1){
                        currentSearchIndex++
                        SerialPortManager.writeSearchResult(shelfSearchResult[currentSearchIndex],shelfSearchResult.size,currentSearchIndex)
                    }
                }
                2->{
                    if (currentSearchIndex > 0 ){
                        currentSearchIndex--
                        SerialPortManager.writeSearchResult(shelfSearchResult[currentSearchIndex],shelfSearchResult.size,currentSearchIndex)
                    }
                }
                3->{
                    val currentShelf = shelfSearchResult[currentSearchIndex]
                    viewModel.openShelf(currentShelf.archiveId,2,currentShelf.boxId)
                }
            }
        }
    }

    /**
     * 配置弹窗
     */
    private fun showConfigDialog() {
        val dialog = MoreDialog(mActivity)
        dialog.isCancelable = false
        dialog.show(parentFragmentManager,"system_config")
    }
    /**
     * 输入密码弹窗
     */
    private fun showPasswordDialog(password:String) {
        checkPasswordDialog = PasswordDialog(false){
            if (password == it){
                Log.d("test","=====密码正确====")
                showConfigDialog()
                checkPasswordDialog.dismiss()
            }else{
                showShortToast("密码有误，请重新输入")
            }
        }
        checkPasswordDialog.show(parentFragmentManager,"check_password")
    }
    /**
     * 设置密码弹窗
     */
    private fun showSetPasswordDialog() {
        setPasswordDialog = PasswordDialog(true){
            if (it.isNotEmpty()){
                Log.d("test","===set password===$it=====")
                lifecycleScope.launch(Dispatchers.IO) {
                    val user = UserInfo()
                    user.userName = "admin"
                    user.loginName = "admin"
                    user.permission_user = 1
                    user.permission_param = 1
                    user.permission_queue = 1
                    user.permission_log = 1
                    user.permission_doc = 1
                    user.permission_shelf = 1
                    user.userId = System.currentTimeMillis()
                    user.password = it
                    UserRepository.insertUser(user)
                    withContext(Dispatchers.Main){
                        showShortToast("密码设置成功")
                        setPasswordDialog.dismiss()
                    }
                }

            }else{
                showShortToast("密码不能为空")
            }
        }
        setPasswordDialog.show(parentFragmentManager,"check_password")

    }
    override fun onClick(v: View) {
        if (MainActivity.currentUser != null &&
            MainActivity.currentUser!!.permission_shelf == 0 &&
            v.id != R.id.bt_setting){
            showShortToast("无权限操作请联系管理员")
            return
        }
        when(v.id){
            R.id.iv_last_page ->{
                if (currentPage > 0){
                    binding.vpShelfList.currentItem = --currentPage
                }
            }
            R.id.iv_next_page ->{
                if (currentPage < allPage-1){
                    binding.vpShelfList.currentItem = ++currentPage
                }
            }

            R.id.bt_more ->{
                lifecycleScope.launch(Dispatchers.IO){
                    val users = UserRepository.getUserByName("admin")
                    withContext(Dispatchers.Main){
                        if (users.isNotEmpty()){
                            val user = users[0]
                            showPasswordDialog(user.password)
                        }else{
                            showSetPasswordDialog()
                        }
                    }

                }

            }

            R.id.bt_ventilation ->{
                SerialPortManager.ventilate()
            }
            R.id.bt_setting ->{
                //设置改净化
                if (config != null && currentShelf != null){
                    val bundle = Bundle()
                    bundle.putByte("address",currentShelf!!.address)
                    findNavController().navigate(R.id.action_mainFragment_to_cleanFragment,
                        bundle
                    )
                }else{
                    ToastUtils.showShort("配置有误")
                }
            }

            R.id.bt_release ->{
                if (config != null){
                    if (binding.btRelease.isSelected){
                        SerialPortManager.openLock()
                    }else{
                        SerialPortManager.lock()
                        ToastUtils.showShort("已发送锁定命令，请稍候")
                    }

                }

            }
            R.id.tv_search,R.id.tv_scan,R.id.tv_task ->{
                if (MainActivity.currentUser != null &&
                    MainActivity.currentUser!!.permission_doc == 0 ){
                    showShortToast("无权限操作请联系管理员")
                    return
                }
                val searchConfigString = SpUtils.LASTING.getString(SearchConfigDialog.SEARCH_CONFIG_KEY,"")
                val searchConfig = if (searchConfigString.isNullOrEmpty()){
                    SearchConfigBean()
                }else{
                    Gson().fromJson(searchConfigString, SearchConfigBean::class.java)
                }

                if (searchConfig.isRemote) {
                    if (searchConfig.searchAddress.isNullOrEmpty()) {
                        ToastUtils.showShort("外部搜索地址为空，请前往设置界面配置")
                    } else {
                        var type = when(v.id){
                            R.id.tv_search ->{
                                isClickSearch = true
                                remoteAddress = searchConfig.searchAddress
                                1
                            }
                            R.id.tv_scan ->{
                                remoteAddress = searchConfig.scanAddress
                                2
                            }
                            R.id.tv_task ->{
                                remoteAddress = searchConfig.taskAddress
                                3
                            }
                            else ->{
                                remoteAddress = searchConfig.searchAddress
                                1
                            }
                        }
                        viewModel.loginFive(type)

                    }
                } else {//本地搜索
                    if (advancedConfig.dataSource == 0) {
                        findNavController().navigate(
                            R.id.action_mainFragment_to_localSearchFragment,
                        )
                    } else {
                        findNavController().navigate(
                            R.id.action_mainFragment_to_localFiveSearchFragment,
                        )
                    }

                }
            }
            else ->{
                //首先查看是否锁定
                currentViewId = v.id
                if (currentLockStatus != 0){
                    ToastUtils.showShort("密集架已锁定，请先解锁")
                    try {
                        //播放语音
                        val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
                            "unlock_first_female.mp3"
                        else
                            "unlock_first_male.mp3")
                        PlaySoundUtil.getInstance().playSound(fd)
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }else{
                    afterLock()
                }
            }
        }
    }

    private fun afterLock() {
        when(currentViewId){
            R.id.bt_open_shelf ->{
                currentShelf?.apply {
                    SerialPortManager.operateShelf(address,0x01)
                    if (advancedConfig.fixPicPath.isNotEmpty() && advancedConfig.molPicPath.isNotEmpty()){
                        if (address < 100){
                            binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                            binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                            binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                        }else{
                            binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                            binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                            binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                        }
                    }else{
                        if (address < 100){
                            binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_move_yd)
                            binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_move_yd)
                            binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_regular_yd)
                        }else{
                            binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_move_yd)
                            binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_regular_yd)
                            binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_move_yd)
                        }
                    }
//                    try {
//                        //播放语音
//                        val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
//                            "start_open_shelf_female.mp3"
//                        else
//                            "start_open_shelf_male.mp3")
//                        PlaySoundUtil.getInstance().playSound(fd)
//                    } catch (e: IOException) {
//                        e.printStackTrace()
//                    }
                    TTSUtils.speak("正在打开第${order}列")
                }

            }
            R.id.bt_close_shelf ->{
                currentShelf?.apply {
//                    if (CleanFragment.isStartWind){
//                        ToastUtils.showShort("正在通风，不能进行此操作")
//                        try {
//                            //播放语音
//                            val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
//                                "winding_no_operate_female.mp3"
//                            else
//                                "winding_no_operate_male.mp3")
//                            PlaySoundUtil.getInstance().playSound(fd)
//                        } catch (e: IOException) {
//                            e.printStackTrace()
//                        }
//                    }else{
//
//                    }
                    SerialPortManager.operateShelf(0x64,0x03)
                    binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_regular_yd)
                    binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_move_yd)
                    binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_move_yd)
//                    try {
//                        //播放语音
//                        val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
//                            "close_shelf_female.mp3"
//                        else
//                            "close_shelf_male.mp3")
//                        PlaySoundUtil.getInstance().playSound(fd)
//                    } catch (e: IOException) {
//                        e.printStackTrace()
//                    }

                }

            }
            R.id.bt_stop ->{
                if (config != null && currentShelf != null){
                    SerialPortManager.stopAllShelf()
                    binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_regular_yd)
                    binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_move_yd)
                    binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_move_yd)
                }

            }


            R.id.iv_move_shelf_left ->{
                moveShelfLeft()
            }
            R.id.iv_move_shelf_right ->{
                moveShelfRight()
            }
        }
    }

    private fun moveShelfLeft() {
        currentShelf?.apply {
            val isLeft = address < 100
            if (currentShelf != null) {
                SerialPortManager.operateMotor(currentShelf!!.address, true, isLeft)
            } else {
                showShortToast("当前选择密集架为空")
            }
            if (advancedConfig.fixPicPath.isNotEmpty() && advancedConfig.molPicPath.isNotEmpty()){
                if (address < 100){
                    binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                    binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                    binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                }else{
                    binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                    binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                    binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                }
            }else{
                if (address < 100) {
                    binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_move_yd)
                    binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_move_yd)
                    binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_regular_yd)
                } else {
                    binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_move_yd)
                    binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_regular_yd)
                    binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_move_yd)
                }
            }
//            try {
//                //播放语音
//                val fd = requireContext().assets.openFd(
//                    if (voiceType == 0)
//                        "shelf_move_left_female.mp3"
//                    else
//                        "shelf_move_left_male.mp3"
//                )
//                PlaySoundUtil.getInstance().playSound(fd)
//            } catch (e: IOException) {
//                e.printStackTrace()
//            }
            TTSUtils.speak("第${order}列左移")
        }
    }

    private fun moveShelfRight() {
        currentShelf?.apply {
            val isLeft = address < 100
            if (currentShelf != null) {
                SerialPortManager.operateMotor(currentShelf!!.address, false, isLeft)
            } else {
                showShortToast("当前选择密集架为空")
            }
            if (advancedConfig.fixPicPath.isNotEmpty() && advancedConfig.molPicPath.isNotEmpty()){
                if (address < 100){
                    binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                    binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                    binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                }else{
                    binding.ivShelfCenter.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                    binding.ivShelfLeft.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.fixPicPath))
                    binding.ivShelfRight.setImageBitmap(BitmapFactory.decodeFile(advancedConfig.molPicPath))
                }
            }else{
                if (address < 100) {
                    binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_move_yd)
                    binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_move_yd)
                    binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_regular_yd)
                } else {
                    binding.ivShelfCenter.setImageResource(R.mipmap.icon_shelf_move_yd)
                    binding.ivShelfLeft.setImageResource(R.mipmap.icon_shelf_regular_yd)
                    binding.ivShelfRight.setImageResource(R.mipmap.icon_shelf_move_yd)
                }
            }
//            try {
//                //播放语音
//                val fd = requireContext().assets.openFd(
//                    if (voiceType == 0)
//                        "shelf_move_right_female.mp3"
//                    else
//                        "shelf_move_right_male.mp3"
//                )
//                PlaySoundUtil.getInstance().playSound(fd)
//            } catch (e: IOException) {
//                e.printStackTrace()
//            }
            TTSUtils.speak("第${order}列右移")
        }
    }

    private fun refreshPieChart(repositoryTotal :Int, archiveTotal:Int, onShelfTotal:Int, percent:String){
        //饼状图
        val dataSets = PieDataSet(mutableListOf(PieEntry(repositoryTotal.toFloat(),""),
            PieEntry(archiveTotal.toFloat(),""),PieEntry(onShelfTotal.toFloat(),"")),"")
        dataSets.colors = mutableListOf(
//            ContextCompat.getColor(requireContext(),R.color.color_3684f1),
            ContextCompat.getColor(requireContext(),R.color.color_00daa4),
            ContextCompat.getColor(requireContext(),R.color.color_ffba00),
            ContextCompat.getColor(requireContext(),R.color.color_00b8e5))
        dataSets.setDrawValues(false)
        dataSets.sliceSpace = 3f
        dataSets.selectionShift = 5f;//设置饼块选中时偏离饼图中心的距离
        binding.chartRepository.description.isEnabled = false;//设置描述
        binding.chartRepository.setExtraOffsets(0f, 0f, 0f, 0f); //设置边距
        binding.chartRepository.setDrawCenterText(false)//设置绘制环中文字
        //设置半透明圆环的半径,看着就有一种立体的感觉
        binding.chartRepository.transparentCircleRadius = DeviceUtil.dip2px(0f).toFloat()
        //这个方法为true就是环形图，为false就是饼图
        binding.chartRepository.isDrawHoleEnabled = true
        binding.chartRepository.setDrawEntryLabels(false)
        //设置环形中间空白颜色是白色
        binding.chartRepository.setHoleColor(Color.TRANSPARENT)
        binding.chartRepository.data = PieData(dataSets)
        binding.chartRepository.legend.isEnabled = false
        binding.chartRepository.isEnabled = false
        //禁止滑动
        binding.chartRepository.isRotationEnabled = false
        //默认选中第一个
        binding.chartRepository.highlightValue(0f,0,false)
        binding.chartRepository.invalidate()

//        val storePercent = "%.2f".format(((onShelfTotal) / borrowTotal.toDouble()) * 100) + "%"
        val storePercent =  "${percent}%"
        binding.tvRepositoryPercent.text = storePercent
    }

    override fun onDestroy() {
        super.onDestroy()
        SerialPortManager.stopRun()
        ServerManager.stopServer()
        WebsocketManager.stopServer()
        VoiceSerialPortManager.stopRun()
        TTSUtils.destroy()
    }

    private fun scheduleSaveDataTask() {
        val periodicWorkRequest = PeriodicWorkRequestBuilder<CleanDataSaveWorker>(1, TimeUnit.MINUTES)
            .build()
        WorkManager.getInstance(requireContext()).enqueueUniquePeriodicWork("clean_environment_save",
            ExistingPeriodicWorkPolicy.REPLACE,periodicWorkRequest)
    }

    private fun showBreathLight() {
        val blcIsEnable = SpUtils.BREATHLIGHT.getInt("blc_is_enable", 0)
        if (blcIsEnable == 1) {
            binding.ivAlarmBreathLight.visibility = View.VISIBLE
        }else {
            binding.ivAlarmBreathLight.visibility = View.GONE
        }
    }
}