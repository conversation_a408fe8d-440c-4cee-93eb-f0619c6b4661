package com.yinduo.compactShelf.view.main.dialog

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.provider.MediaStore
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.databinding.DialogScreensaverBinding
import com.yinduo.compactShelf.room.entity.ScreenSaverConfig
import com.yinduo.compactShelf.room.repository.ScreenSaverConfigRepository
import com.yinduo.compactShelf.utils.DeviceUtil
import com.yinduo.compactShelf.utils.dtoast.showShortToast
import com.yinduo.compactShelf.view.main.viewModel.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

private const val TAG = "ScreenSaverDialog"

class ScreenSaverDialog() : DialogFragment(), View.OnClickListener {
    private lateinit var rotationTimeItems:MutableList<Int>
    private lateinit var waitTimeItems:MutableList<Int>
    private lateinit var viewModel: MainViewModel
    private var config: ScreenSaverConfig? = null
    private var picCounts = 0
    private var imagePaths: MutableList<String> = mutableListOf()
    private var currentIndex = 0
    private lateinit var binding: DialogScreensaverBinding
    protected lateinit var mContext: Context
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, R.style.dialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.dialog_screensaver,
            null,
            false
        )
        this.mContext = this.requireContext()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.click = this

        viewModel = ViewModelProvider(requireActivity())[MainViewModel::class.java]
        viewModel.screenSaverConfigLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                refreshView(it)
            } else {
                refreshView(ScreenSaverConfig(id = -1))
            }
        }

        binding.spRotationTime.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
        rotationTimeItems = (0..15).toMutableList()
        val adapter = ArrayAdapter(mContext, R.layout.item_enable_match_parent, rotationTimeItems)
        adapter.setDropDownViewResource(R.layout.item_spinner_select)
        binding.spRotationTime.adapter = adapter

        binding.spWaitTime.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
        waitTimeItems = (0..300).toMutableList()
        val waitTimeAdapter =
            ArrayAdapter(mContext, R.layout.item_enable_match_parent, waitTimeItems)
        waitTimeAdapter.setDropDownViewResource(R.layout.item_spinner_select)
        binding.spWaitTime.adapter = waitTimeAdapter

        //




    }



    override fun onResume() {
        super.onResume()
        val window = dialog!!.window
        window!!.setGravity(Gravity.CENTER)
        dialog!!.window!!.decorView.setPadding(DeviceUtil.dip2px(20f), 0, DeviceUtil.dip2px(20f), 0)
        dialog!!.window!!.setLayout(DeviceUtil.dip2px(348f), DeviceUtil.dip2px(190f))
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_PICK_IMAGES && resultCode == Activity.RESULT_OK && data != null) {
            imagePaths.clear()
            data.clipData?.let {
                picCounts = it.itemCount
                for (i in 0 until it.itemCount) {
                    val imageUri = it.getItemAt(i).uri
                    imagePaths.add("$imageUri")
                }
            } ?: run {
                picCounts = 1
                val imageUri = data.data
                imageUri?.let {
                    imagePaths.add(imageUri.toString())
                }
            }
            binding.etPicCounts.setText(picCounts.toString())
        }
    }

    private fun refreshView(config: ScreenSaverConfig) {
        binding.apply {
            <EMAIL> = config
            etPicCounts.setText(config.picCounts.toString())
            spRotationTime.setSelection(rotationTimeItems.indexOf(config.picInterval))
            spWaitTime.setSelection(waitTimeItems.indexOf(config.startUpTime))
            imagePaths = config.picPath
        }
    }

    private fun pickImages() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
        intent.type = "image/*"
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
        if(intent.resolveActivity(requireActivity().packageManager) != null){
            startActivityForResult(intent, REQUEST_CODE_PICK_IMAGES)
        }else{
            val intent = Intent(Intent.ACTION_GET_CONTENT)
            intent.type = "image/*"
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            startActivityForResult(intent, REQUEST_CODE_PICK_IMAGES)
        }

    }

    companion object {
        private const val REQUEST_CODE_PERMISSION = 1
        private const val REQUEST_CODE_PICK_IMAGES = 2
        private const val INTERVAL = 3000
        private const val START_TIME = 0
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.bt_complete -> {
                saveConfig()

            }

            R.id.bt_choose_pic -> {
                pickImages()
            }

            R.id.bt_cancel -> {
                dismiss()
            }

            R.id.iv_close -> {
                dismiss()
            }
        }
    }

    private fun saveConfig() {
        if(config == null){
            config = ScreenSaverConfig(-1)
        }
        binding.apply {
            config!!.picCounts = etPicCounts.text.toString().toInt()
            config!!.picInterval = spRotationTime.selectedItem.toString().toInt()
            config!!.startUpTime = spWaitTime.selectedItem.toString().toInt()
            config!!.picPath = imagePaths
        }

        lifecycleScope.launch(Dispatchers.IO){
            kotlin.runCatching {
                if(config!!.id == -1){
                    config!!.id = 1
                    ScreenSaverConfigRepository.insertConfig(config!!)
                }else{
                    ScreenSaverConfigRepository.updateConfig(config!!)
                }
            }.onSuccess {
                withContext(Dispatchers.Main){
                    showShortToast("保存成功")
                }
            }.onFailure {
                withContext(Dispatchers.Main){
                    showShortToast("保存失败===error=${it.message}")
                }
            }
        }
    }


}