package com.yinduo.compactShelf.view.main

import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import android.widget.ArrayAdapter
import android.widget.Spinner
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.yinduo.compactShelf.BuildConfig
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.base.BaseFragment
import com.yinduo.compactShelf.databinding.DialogSystemConfigBinding
import com.yinduo.compactShelf.manager.SerialPortManager
import com.yinduo.compactShelf.room.entity.SystemConfig
import com.yinduo.compactShelf.room.repository.SystemConfigRepository
import com.yinduo.compactShelf.utils.DeviceUtil
import com.yinduo.compactShelf.utils.PlaySoundUtil
import com.yinduo.compactShelf.utils.SpUtils
import com.yinduo.compactShelf.utils.dtoast.showShortToast
import com.yinduo.compactShelf.view.main.dialog.PowerSettingDialog
import com.yinduo.compactShelf.view.main.viewModel.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException

class SystemConfigFragment : BaseFragment<DialogSystemConfigBinding, MainViewModel>(),OnClickListener {
    private var lastSystemName = 0
    private var config: SystemConfig? = null
    private lateinit var powerSettingDialog: PowerSettingDialog
    override fun getLayoutId(): Int {
        return R.layout.dialog_system_config
    }

    override fun getViewModel(): Class<MainViewModel> {
        return MainViewModel::class.java
    }

    override fun initView() {
        binding.click = this
        viewModel.configLiveData.observe(viewLifecycleOwner){
            if (it != null){
                refreshView(it)
            }else {
                refreshView(SystemConfig(id = -1,device_ip = DeviceUtil.iP?:""))
            }
        }
        setSpinnerItem(binding.etActiveScreensaver)
        setSpinnerItem(binding.etSmokeAlarm)

        binding.etSensorType.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
//        val items = listOf("本地","组件","远程")
        val items = listOf("本地","组件")
        val adapter = ArrayAdapter(mContext,R.layout.item_enable,items)
        adapter.setDropDownViewResource(R.layout.item_spinner_select)
        binding.etSensorType.adapter = adapter

        binding.etSystemName.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
        val systemItems = listOf("除尘除霉型智能密集架控制系统","除酸除尘型智能密集架控制系统","醋酸分解型智能密集架控制系统")
        val systemAdapter = ArrayAdapter(mContext,R.layout.item_enable,systemItems)
        systemAdapter.setDropDownViewResource(R.layout.item_spinner_select)
        binding.etSystemName.adapter = systemAdapter

        binding.etCleanName.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
        val cleanItems = listOf("除尘除霉","除酸除尘","醋酸分解")
        val cleanAdapter = ArrayAdapter(mContext,R.layout.item_enable,cleanItems)
        cleanAdapter.setDropDownViewResource(R.layout.item_spinner_select)
        binding.etCleanName.adapter = cleanAdapter

        binding.tvVersion.text = "版本号：${BuildConfig.VERSION_NAME}"

    }
    private fun refreshView(config: SystemConfig){
        binding.apply {
            <EMAIL> = config
            etSerialPort.setText(config.serial_port)
            etCameraRotate.setText(config.camera_rotate.toString())
            etServerIp.setText(config.service_address)
            etServerPort.setText(config.service_port.toString())
            etSelfIp.setText(config.device_ip)
            etActiveScreensaver.setSelection(config.active_screensaver)
            etSensorType.setSelection(config.sensor_type)
            etSystemName.setSelection(config.system_name)
            etCleanName.setSelection(config.clean_name)
            etStandbyTime.setText(config.standby_time.toString())
            etSmokeAlarm.setSelection(config.smoke_alarm)
            tvStandbyClose.isSelected = config.standby_close_shelf == 1
            tvStandbyShutdown.isSelected = config.standby_shutdown_power == 1
            tvStopEnable.isSelected = config.can_stop == 1
            etAreaOrder.setText(config.area_order.toString())
            etAreaNumber.setText(config.area_number.toString())
            etColumnOrder.setText(config.column_order.toString())
            etHelpOrder.setText(config.help_column_order.toString())
            etSectionNumber.setText(config.section_number.toString())
            etLayerNumber.setText(config.layer_number.toString())
            etLedNumber.setText(config.led_number.toString())
            etVirtualWidth.setText(config.virtual_width.toString())
            etTempReparation.setText(config.temp_reparation.toString())
            etRhReparation.setText(config.rh_reparation.toString())
            etConnectRepository.setText(config.connect_repository.toString())
            tvAutoOpenShelf.isSelected = config.auto_open_shelf == 1
            tvScanEnable.isSelected = config.scan_enable == 1
            tvLambReverse.isSelected = config.position_lamb_reverse == 1
            tvAReverse.isSelected = config.a_reverse == 1
            tvBReverse.isSelected = config.b_reverse == 1
            tvVoiceFemale.isSelected = config.voice == 0
            tvVoiceMale.isSelected = config.voice == 1
            etDelayCloseLamb.setText(config.delay_close_lamb.toString())
            val faceExit = SpUtils.CACHE.getBoolean("tv_face_exit_saver", false)
            tvFaceExitSaver.isSelected = faceExit
        }

    }

    private fun saveConfig(){
       if (config == null){
           config = SystemConfig(id = -1, device_ip = DeviceUtil.iP?:"")
       }
       binding.apply {
           config!!.serial_port = etSerialPort.text.toString()
           config!!.camera_rotate = etCameraRotate.text.toString().toIntOrNull()?:0
           config!!.service_address = etServerIp.text.toString()
           config!!.service_port = etServerPort.text.toString().toIntOrNull()?:0
           config!!.device_ip = etSelfIp.text.toString()
           config!!.active_screensaver = etActiveScreensaver.selectedItemPosition
           config!!.standby_time = etStandbyTime.text.toString().toIntOrNull()?:0
           config!!.smoke_alarm = etSmokeAlarm.selectedItemPosition
           config!!.sensor_type = etSensorType.selectedItemPosition
           config!!.system_name = etSystemName.selectedItemPosition
           config!!.clean_name = etCleanName.selectedItemPosition
           config!!.standby_close_shelf = if (tvStandbyClose.isSelected) 1 else 0
           config!!.standby_shutdown_power = if (tvStandbyShutdown.isSelected) 1 else 0
           config!!.can_stop = if (tvStopEnable.isSelected) 1 else 0
           config!!.area_order = etAreaOrder.text.toString().toIntOrNull()?:0
           config!!.area_number = etAreaNumber.text.toString().toIntOrNull()?:0
           config!!.column_order = etColumnOrder.text.toString().toIntOrNull()?:0
           config!!.help_column_order = etHelpOrder.text.toString().toIntOrNull()?:0
           config!!.section_number = etSectionNumber.text.toString().toIntOrNull()?:0
           config!!.layer_number = etLayerNumber.text.toString().toIntOrNull()?:0
           config!!.led_number = etLedNumber.text.toString().toIntOrNull()?:0
           config!!.virtual_width = etVirtualWidth.text.toString().toIntOrNull()?:0
           config!!.temp_reparation = etTempReparation.text.toString().toIntOrNull()?:0
           config!!.rh_reparation = etRhReparation.text.toString().toIntOrNull()?:0
           config!!.connect_repository = etConnectRepository.text.toString().toIntOrNull()?:0
           config!!.auto_open_shelf = if (tvAutoOpenShelf.isSelected) 1 else 0
           config!!.scan_enable = if (tvScanEnable.isSelected) 1 else 0
           config!!.position_lamb_reverse = if (tvLambReverse.isSelected) 1 else 0
           config!!.a_reverse = if (tvAReverse.isSelected) 1 else 0
           config!!.b_reverse = if (tvBReverse.isSelected) 1 else 0
           config!!.voice = if (tvVoiceFemale.isSelected) 0 else 1
           config!!.delay_close_lamb = etDelayCloseLamb.text.toString().toIntOrNull()?:0
       }
        SpUtils.CACHE.putBoolean("tv_face_exit_saver", binding.tvFaceExitSaver.isSelected)
        lifecycleScope.launch (Dispatchers.IO){
//            if(config!!.led_number!=0 && config!!.virtual_width != 0){
//                SerialPortManager.lftColumnCounts(config!!.led_number)
//                SerialPortManager.rhtColumnCounts(config!!.virtual_width)
//            }
            kotlin.runCatching {
                if (config!!.id == -1){
                    config!!.id = 1
                    SystemConfigRepository.insertConfig(config!!)
                }else{
                    SystemConfigRepository.updateConfig(config!!)
                }
            }.onSuccess {
                withContext(Dispatchers.Main){
                    showShortToast("保存成功")
                }
            }.onFailure {
                withContext(Dispatchers.Main){
                    showShortToast("保存失败===error=${it.message}")
                }
            }

        }



    }

    private fun setSpinnerItem(spinner: Spinner){
        spinner.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
        val items = listOf("禁用","启用")
        val adapter = ArrayAdapter(mContext,R.layout.item_enable,items)
        adapter.setDropDownViewResource(R.layout.item_spinner_select)
        spinner.adapter = adapter
    }
    override fun initData() {
    }

    override fun onClick(v: View) {
        if (MainActivity.currentUser != null &&
            MainActivity.currentUser!!.permission_param == 0){
            showShortToast("无权限操作请联系管理员")
            return
        }
        when(v.id){
            R.id.tv_standby_close ->{
                binding.tvStandbyClose.isSelected = ! binding.tvStandbyClose.isSelected
            }
            R.id.tv_standby_shutdown ->{
                binding.tvStandbyShutdown.isSelected = ! binding.tvStandbyShutdown.isSelected
            }
            R.id.tv_stop_enable ->{
                binding.tvStopEnable.isSelected = ! binding.tvStopEnable.isSelected
            }
            R.id.tv_auto_open_shelf ->{
                binding.tvAutoOpenShelf.isSelected = ! binding.tvAutoOpenShelf.isSelected
            }
            R.id.tv_scan_enable ->{
                binding.tvScanEnable.isSelected = ! binding.tvScanEnable.isSelected
            }
            R.id.tv_lamb_reverse ->{
                binding.tvLambReverse.isSelected = ! binding.tvLambReverse.isSelected
            }
            R.id.tv_a_reverse ->{
                binding.tvAReverse.isSelected = ! binding.tvAReverse.isSelected
            }
            R.id.tv_b_reverse ->{
                binding.tvBReverse.isSelected = ! binding.tvBReverse.isSelected
            }
            R.id.tv_voice_female ->{
                binding.tvVoiceFemale.isSelected = ! binding.tvVoiceFemale.isSelected
                binding.tvVoiceMale.isSelected = ! binding.tvVoiceFemale.isSelected
            }
            R.id.tv_voice_male ->{
                binding.tvVoiceMale.isSelected = ! binding.tvVoiceMale.isSelected
                binding.tvVoiceFemale.isSelected = ! binding.tvVoiceMale.isSelected
            }
            R.id.tv_power_setting -> {
                powerSettingDialog = PowerSettingDialog()
                powerSettingDialog.show(requireActivity().supportFragmentManager,"power_setting_dialog")
            }
            R.id.bt_save ->{
                saveConfig()
            }
            R.id.bt_get_ip ->{
                if (!DeviceUtil.iP.isNullOrEmpty()){
                    binding.etSelfIp.setText(DeviceUtil.iP)
                }else{
                    showShortToast("本机ip为空")
                }
            }
            R.id.tv_listen ->{
                try {
                    //播放语音
                    val fd = requireContext().assets.openFd(if (MainFragment.voiceType == 0)
                        "welcome_female.mp3"
                    else
                        "welcome_male.mp3")
                    PlaySoundUtil.getInstance().playSound(fd)
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            R.id.bt_send_config ->{
                val lfc = binding.etLedNumber.text.toString()
                val rcc = binding.etVirtualWidth.text.toString()
                lifecycleScope.launch(Dispatchers.IO){
                    if(lfc.isNotEmpty() && rcc.isNotEmpty()){
                        SerialPortManager.lftColumnCounts(lfc.toInt())
                        SerialPortManager.rhtColumnCounts(rcc.toInt())
                    }
                    val sensorTypeVal = binding.etSensorType.selectedItemPosition
                    val sensorType = SerialPortManager.molSensorType
                    sensorType[0] = 0x64
                    sensorType[5] = sensorTypeVal.toByte()
                    if(sensorTypeVal in 0..1){
                        SerialPortManager.molGeneralCommand(sensorType)
                    }
                }
            }
            R.id.bt_get_config ->{
                SerialPortManager.readFixColParams()
                val params = SerialPortManager.getFixColParams()
                Log.d("test", "主板参数:${Gson().toJson(params)}")
                binding.etLedNumber.setText(params.lftColCounts.toString())
                binding.etVirtualWidth.setText(params.rhtColCounts.toString())
            }
            R.id.tv_face_exit_saver ->{
                binding.tvFaceExitSaver.isSelected = ! binding.tvFaceExitSaver.isSelected
            }
        }
    }
}