<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.front.autofocus"
        android:required="true" />
    <queries>
        <intent>
            <action android:name="android.intent.action.TTS_SERVICE"/>
        </intent>
    </queries>

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.CHANGE_COMPONENT_ENABLED_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <application
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:name=".BaseApplication"
        android:allowBackup="true"
        tools:replace="android:allowBackup"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/icon_yundang"
        android:roundIcon="@mipmap/icon_yundang"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31" >
        <activity android:name=".view.main.MainActivity"
            android:theme="@style/ActivityTheme"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
<!--        <activity-->
<!--            android:enabled="false"-->
<!--            android:exported="true"-->
<!--            android:label="@string/app_name1"-->
<!--            android:name=".view.main.MainActivity1"-->
<!--            android:targetActivity=".view.main.MainActivity">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--                <action android:name="android.intent.action.VIEW" />-->
<!--                <category android:name="android.intent.category.HOME" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
<!--        <activity-->
<!--            android:enabled="false"-->
<!--            android:exported="true"-->
<!--            android:label="@string/app_name2"-->
<!--            android:name=".view.main.MainActivity2"-->
<!--            android:targetActivity=".view.main.MainActivity">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--                <action android:name="android.intent.action.VIEW" />-->
<!--                <category android:name="android.intent.category.HOME" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
    </application>

</manifest>