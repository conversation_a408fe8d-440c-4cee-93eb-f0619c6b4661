package com.yinduo.compactShelf.view.main

import android.graphics.BitmapFactory
import android.util.Log
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.base.BaseFragment
import com.yinduo.compactShelf.base.customlivedata.SuperLiveDataManager
import com.yinduo.compactShelf.databinding.FragmentShelfMotionBinding
import com.yinduo.compactShelf.room.repository.AdvancedConfigRepository
import com.yinduo.compactShelf.utils.PlaySoundUtil
import com.yinduo.compactShelf.utils.TTSUtils
import com.yinduo.compactShelf.view.main.viewModel.MainViewModel
import com.yinduo.compactShelf.view.main.viewModel.ShelfMotionViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException

class ShelfMotionFragment : BaseFragment<FragmentShelfMotionBinding, MainViewModel>() {
    private lateinit var shelfMotionViewModel: ShelfMotionViewModel
    private var isplayed: Boolean = false
    private var isShelfMoveDone: Boolean = false
    private var isSendedCmd = false
    override fun getLayoutId(): Int {
        return R.layout.fragment_shelf_motion
    }

    override fun getViewModel(): Class<MainViewModel>? {
        return MainViewModel::class.java
    }

    override fun initView() {
        shelfMotionViewModel = ShelfMotionViewModel(findNavController())
        binding.viewModel = shelfMotionViewModel
    }

    override fun initData() {
        lifecycleScope.launch(Dispatchers.IO) {
            val config = AdvancedConfigRepository.getConfig()
            if (config?.fixPicPath?.isNotEmpty() == true && config.molPicPath.isNotEmpty()) {
                withContext(Dispatchers.Main) {
                    binding.ivFixIcon.setImageBitmap(BitmapFactory.decodeFile(config.fixPicPath))
                    binding.ivMoveIcon.setImageBitmap(BitmapFactory.decodeFile(config.molPicPath))
                }
            }
        }
        SuperLiveDataManager.motionState.observeOneOff(viewLifecycleOwner) {
            Log.d("test","======shelfmotion fragment shelfMotionState====${it}==========")
            when (it) {
                0 -> {
                    isplayed = false
                    isSendedCmd = false
                    if(isShelfMoveDone) return@observeOneOff
                    isShelfMoveDone = true
                    CoroutineScope(Dispatchers.IO).launch {
                        try {
                            //播放语音
                            val fd = requireContext().assets.openFd(
                                if (MainFragment.voiceType == 0)
                                    "shelf_motion_done_female.mp3"
                                else
                                    "shelf_motion_done_male.mp3"
                            )
                            PlaySoundUtil.getInstance().playSound(fd)
                            delay(1500)
                            withContext(Dispatchers.Main){
                                findNavController().navigate(R.id.action_shelfMotionFragment_to_mainFragment)
                            }
                        } catch (e: IOException) {
                            e.printStackTrace()
                        }
                    }
                }

                1 -> {
                    if (isplayed) return@observeOneOff
                    isplayed = true
                    try {
                        //播放语音
                        val fd = requireContext().assets.openFd(
                            if (MainFragment.voiceType == 0)
                                "shelf_opening_female.mp3"
                            else
                                "shelf_opening_male.mp3"
                        )
                        PlaySoundUtil.getInstance().playSound(fd)
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }

                2,
                3 -> {
                    if (isplayed) return@observeOneOff
                    isplayed = true
                    try {
                        //播放语音
                        val fd = requireContext().assets.openFd(
                            if (MainFragment.voiceType == 0)
                                "shelf_closing_female.mp3"
                            else
                                "shelf_closing_male.mp3"
                        )
                        PlaySoundUtil.getInstance().playSound(fd)
                        binding.tvOpenPosition.text = "正在闭架"
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
                4 -> {
                    //when hardware can't stop send that command by software
                    //指令规则由梁工、和孙工提起加入到软件逻辑中
//                    if(isSendedCmd) return@observeOneOff
//                    Log.d("shelfMotion", "通风开架")
//                    CoroutineScope(Dispatchers.IO).launch{
//                        delay(20000)
//                        SerialPortManager.stopAllShelf()
//                        SerialPortManager.lock()
//                        Log.d("shelfMotion", "下发停止")
//                    }
//                    isSendedCmd = true
                }
                5 -> {
                    Log.d("shelfMotion", "通风闭架")
                    if (isplayed) return@observeOneOff
                    isplayed = true
                    TTSUtils.speak(getString(R.string.starting_ventilate))
                }
            }
        }

        SuperLiveDataManager.openPosition.observeOneOff(viewLifecycleOwner) {
            Log.d("ShelfMotionFragment", "打开位置${it}")
            binding.tvOpenPosition.text = it
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }


}