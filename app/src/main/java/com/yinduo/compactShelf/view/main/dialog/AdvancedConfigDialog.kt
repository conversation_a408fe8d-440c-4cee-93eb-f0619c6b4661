package com.yinduo.compactShelf.view.main.dialog

import android.app.Activity
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.proembed.service.MyService
import com.yinduo.compactShelf.BaseApplication
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.databinding.DialogAdvancedConfigBinding
import com.yinduo.compactShelf.manager.voice.VoiceSerialPortManager
import com.yinduo.compactShelf.room.entity.AdvancedConfig
import com.yinduo.compactShelf.room.repository.AdvancedConfigRepository
import com.yinduo.compactShelf.utils.DeviceUtil
import com.yinduo.compactShelf.utils.dtoast.showShortToast
import com.yinduo.compactShelf.view.main.viewModel.MainViewModel
import com.ys.rkapi.MyManager
import kotlinx.android.synthetic.main.dialog_advanced_config.et_qr_baud_rate
import kotlinx.android.synthetic.main.dialog_advanced_config.et_qrcode_port
import kotlinx.android.synthetic.main.dialog_advanced_config.et_rfid_port
import kotlinx.android.synthetic.main.dialog_advanced_config.tv_locate_light_enable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

class AdvancedConfigDialog() : DialogFragment(), View.OnClickListener {
    private var scanEnable: Int = 0
    private lateinit var viewModel: MainViewModel
    private lateinit var binding: DialogAdvancedConfigBinding
    private var config: AdvancedConfig? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, R.style.dialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.dialog_advanced_config,
            null,
            false
        )
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.click = this

        viewModel = ViewModelProvider(requireActivity())[MainViewModel::class.java]

        viewModel.advancedConfigLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                refreshView(it)
            } else {
                refreshView(AdvancedConfig(-1))
            }
        }
        viewModel.configLiveData.observe(viewLifecycleOwner){
            scanEnable = it.scan_enable
           binding.etRfidPort.visibility = if (scanEnable == 1) View.VISIBLE else View.INVISIBLE
           binding.tvRfidPort.visibility = if (scanEnable == 1) View.VISIBLE else View.INVISIBLE
           binding.tvBaudRate.visibility = if (scanEnable == 1) View.VISIBLE else View.INVISIBLE
           binding.etQrBaudRate.visibility = if (scanEnable == 1) View.VISIBLE else View.INVISIBLE
        }

        binding.spScreenType.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
        val items = listOf("Y156M1222A_V1","M16G30","startKylin215")
        val adapter = ArrayAdapter(requireContext(), R.layout.item_enable, items)
        adapter.setDropDownViewResource(R.layout.item_spinner_select)
        binding.spScreenType.adapter = adapter

        binding.spDataSource.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
        val dataSourceItems = listOf("3.0接口","5.0接口")
        val dataSourceAdapter = ArrayAdapter(requireContext(), R.layout.item_enable, dataSourceItems)
        dataSourceAdapter.setDropDownViewResource(R.layout.item_spinner_select)
        binding.spDataSource.adapter = dataSourceAdapter

        binding.spLocateLightType.dropDownVerticalOffset = DeviceUtil.dip2px(15f)
        val lltItems = listOf("自动", "手动")
        val lltAdapter = ArrayAdapter(requireContext(), R.layout.item_enable_match_parent, lltItems)
        lltAdapter.setDropDownViewResource(R.layout.item_spinner_select)
        binding.spLocateLightType.adapter = lltAdapter
    }

    override fun onResume() {
        super.onResume()
        val window = dialog!!.window
        window!!.setGravity(Gravity.CENTER)
        dialog!!.window!!.decorView.setPadding(DeviceUtil.dip2px(20f), 0, DeviceUtil.dip2px(20f), 0)
        dialog!!.window!!.setLayout(DeviceUtil.dip2px(522.5f), DeviceUtil.dip2px(446f))
    }

    private fun refreshView(config: AdvancedConfig) {
        binding.apply {
            <EMAIL> = config
            etDevicePort.setText(config.clientPort.toString())
            spDataSource.setSelection(config.dataSource)
            spScreenType.setSelection(config.screenType)
            et_qrcode_port.setText(config.qrSerialPort)
            et_rfid_port.setText(config.rfidSerialPort)
            et_qr_baud_rate.setText(config.qrBaudRate.toString())
            spFixIcon.text = config.fixPicPath
            spMolIcon.text = config.molPicPath
            etVoiceDevicePort.setText(config.voicePort)
            tvVoiceDeviceEnable.isSelected = config.isEnableVoice == 1
            tvLinkageTrkCamera.isSelected = config.isEnableOrbitRobot == 1
            spLocateLightType.setSelection(config.locateLightType)
            tvLocateLightEnable.isSelected = config.isEnableLocateLight == 1

        }
    }

    private fun pickImages(requestCode: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val mediaDocumentsUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                setDataAndType(mediaDocumentsUri, "image/*")
            }
            startActivityForResult(intent, requestCode)
        }else{
            val intent = Intent(Intent.ACTION_PICK)
            intent.type = "image/*"
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            if(intent.resolveActivity(requireActivity().packageManager) != null){
                startActivityForResult(intent, requestCode)
            }else{
                val intent = Intent(Intent.ACTION_GET_CONTENT)
                intent.type = "image/*"
                intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
                startActivityForResult(intent, requestCode)
            }

        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val targetDirc = Environment.getExternalStorageDirectory().absolutePath +File.separator+ "shelfPics"
        if (requestCode == 3 && resultCode == Activity.RESULT_OK && data != null) {
            //fix col
            Log.d("test", "data: ${data.data}")
            data.data?.let {
                val fileName = "fixShelf.png"
                val fixPicPath = it.toString()
                Log.d("test", "fixPicPath: $fixPicPath")
                lifecycleScope.launch(Dispatchers.IO){
                    copyImageToDirectory(requireContext(), it, fileName)
                    val text = targetDirc + File.separator + fileName
                    withContext(Dispatchers.Main){
                        binding.spFixIcon.text = text
                    }
                }
            }

        }else if(requestCode == 4 && resultCode == Activity.RESULT_OK && data != null) {
            //mol col
            data.data?.let {
                val molPicPath = it.toString()
                lifecycleScope.launch(Dispatchers.IO){
                    val fileName = "molShelf.png"
                    copyImageToDirectory(requireContext(), it, fileName)
                    val text = targetDirc + File.separator + fileName
                    withContext(Dispatchers.Main){
                        binding.spMolIcon.text = text
                    }
                }
            }
        }
    }

    // 定义一个函数，接收上下文、选择的图片Uri和目标目录路径作为参数
    fun copyImageToDirectory(context: Context, imageUri: Uri, fileName: String) {
        val contentResolver: ContentResolver = context.contentResolver
        val inputStream: InputStream? = contentResolver.openInputStream(imageUri)
        var outputStream: FileOutputStream? = null

        try {
            val targetDirc = Environment.getExternalStorageDirectory().absolutePath
            Log.d("test", "目标路径: $targetDirc")
            val folder = File(targetDirc, "shelfPics")
            if(!folder.exists()){
                folder.mkdir()
            }
            outputStream = FileOutputStream(File(folder, fileName))
            val buffer = ByteArray(1024)
            var bytesRead: Int
            while (inputStream?.read(buffer).also { bytesRead = it!! } != -1) {
                outputStream.write(buffer, 0, bytesRead)
            }
            outputStream.flush()
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            try {
                inputStream?.close()
                outputStream?.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    private fun saveConfig() {
        if(config == null){
            config = AdvancedConfig(-1)
        }
        binding.apply {
            config!!.clientPort = etDevicePort.text.toString().toInt()
            config!!.dataSource = spDataSource.selectedItemPosition
            config!!.screenType = spScreenType.selectedItemPosition
            config!!.qrSerialPort = et_qrcode_port.text.toString().trim()
            config!!.rfidSerialPort= et_rfid_port.text.toString().trim()
            config!!.qrBaudRate = etQrBaudRate.text.toString().trim().toInt()
            config!!.fixPicPath = spFixIcon.text.toString()
            config!!.molPicPath = spMolIcon.text.toString()
            config!!.voicePort = etVoiceDevicePort.text.toString()
            config!!.isEnableVoice = if(tvVoiceDeviceEnable.isSelected) 1 else 0
            config!!.isEnableOrbitRobot = if(tvLinkageTrkCamera.isSelected) 1 else 0
            config!!.locateLightType = spLocateLightType.selectedItemPosition
            config!!.isEnableLocateLight = if(tv_locate_light_enable.isSelected) 1 else 0
        }
        lifecycleScope.launch(Dispatchers.IO) {
            when(config?.screenType){
                0 ->{
                    val manager = MyManager.getInstance(BaseApplication.context)
                    manager.daemon("com.yinduo.compactShelf", 1)
                    manager.hideStatusBar(false)
                }
                2 ->{
                    val myService = MyService(BaseApplication.context)
                    myService.hideNavBar = true
                    myService.setAppListen("com.yinduo.compactShelf", 30)
                    myService.appBoot = "com.yinduo.compactShelf"
                }
            }
            kotlin.runCatching {
                if (config!!.id == -1) {
                    config!!.id = 1
                    AdvancedConfigRepository.insertConfig(config!!)
                } else {
                    AdvancedConfigRepository.updateConfig(config!!)
                }

            }.onSuccess {
                withContext(Dispatchers.Main) {
                    showShortToast("保存成功")
                }
            }.onFailure {
                withContext(Dispatchers.Main) {
                    showShortToast("保存失败===error=${it.message}")
                }
            }
        }
    }
    override fun onClick(v: View) {
        when (v.id) {
            R.id.bt_complete -> {
                saveConfig()
            }

            R.id.bt_cancel -> {
                dismiss()
            }

            R.id.iv_close -> {
                dismiss()
            }
            R.id.sp_fix_icon ->{
                pickImages(3)
            }
            R.id.sp_mol_icon ->{
                pickImages(4)
            }
            R.id.tv_voice_device_enable ->{
                //Tip
                val state = !binding.tvVoiceDeviceEnable.isSelected
                Log.d("test", "voice enable state: $state")
                binding.tvVoiceDeviceEnable.isSelected = state
                if(state){
                    VoiceSerialPortManager.vPO()
                }else{
                    VoiceSerialPortManager.vPOFF()
                }
            }
            R.id.tv_linkage_trk_camera ->{
                binding.tvLinkageTrkCamera.isSelected = !binding.tvLinkageTrkCamera.isSelected
            }
            R.id.tv_locate_light_enable ->{
                binding.tvLocateLightEnable.isSelected = !binding.tvLocateLightEnable.isSelected
            }
        }
    }
}