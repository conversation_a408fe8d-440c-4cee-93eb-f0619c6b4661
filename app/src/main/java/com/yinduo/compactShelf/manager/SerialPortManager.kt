package com.yinduo.compactShelf.manager

import android.util.Log
import android_serialport_api.SerialBaseThread
import com.yinduo.compactShelf.base.customlivedata.SuperLiveDataManager
import com.yinduo.compactShelf.base.customlivedata.base.postOneOff
import com.yinduo.compactShelf.room.entity.Air
import com.yinduo.compactShelf.room.repository.AirRepository
import com.yinduo.compactShelf.utils.CRC16Util
import com.yinduo.compactShelf.utils.FileUtils
import com.yinduo.compactShelf.utils.TimeUtils
import com.yinduo.compactShelf.view.main.bean.FixColParamBean
import com.yinduo.compactShelf.view.main.bean.OrderBean
import com.yinduo.compactShelf.view.main.bean.ShelfColDetailStatusBean
import com.yinduo.compactShelf.view.main.bean.ShelfColumnStatusBean
import com.yinduo.compactShelf.view.main.bean.ShelfMoveDistanceStatusBean
import com.yinduo.compactShelf.view.main.bean.ShelfSearchResultBean
import com.yinduo.compactShelf.view.main.bean.ShelfStatusBean
import com.yinduo.compactShelf.view.main.bean.VoiceSerialBean
import java.io.IOException
import java.nio.charset.Charset
import java.util.concurrent.locks.ReentrantLock


object SerialPortManager {
    private var lockStatus: Int = -1
    private var lastMotionStatus: Int =-1
    var operateAddress: Byte = 0
    var operateType: Int = 0
    var sendOperateOrder: Boolean = false
    private var lastAddress: Byte = 0//批量下写Led的最后一个address
    private var lastOrderTime: Long = 0//移动列通知类型的命令接收时间
    private var currentShelfAddr: Byte = 0
    var isShowWebResult: Boolean = false
    var isShowLog: Boolean = false
    private var repeatCount: Int = 0
    private lateinit var currentMessage: QueueMessage
    private var isWrite: Boolean = false //是否已经写入
    private const val DEFAULT_SERIAL_INTERVAL_TIMES = 100
    private var mySerialThread: MySerialThread? = null
    var isDebug: Boolean = false //是否发送debug数据
    var isOnlyControl: Boolean = false //是否只筛选控制命令 目前按06下写命令来区分
    private val syncLock = ReentrantLock()

    var DEFAULT_SERIALPATH = ""
    var DEFUALT_BAUDRATE = 0

    private val rhtColumnCounts = byteArrayOf(0x64, 0x06, 0x10, 0x6A, 0x00, 0x00)
    private val lftColumnCounts = byteArrayOf(0x64, 0x06, 0x10, 0x6B, 0x00, 0x00)
    private var fixColumnParams = byteArrayOf(0x64, 0x04, 0x10, 0x6A, 0x00, 0x02)

    //--------移动列
    //左层数
    val molLeftLayerCounts = byteArrayOf(0x00, 0x06, 0x10, 0x87.toByte(), 0x00, 0x00)

    //左层顺序
    val molLeftLayerOrder = byteArrayOf(0x00, 0x06, 0x10, 0x88.toByte(), 0x00, 0x00)

    //左节数
    val molLeftsectionCounts = byteArrayOf(0x00, 0x06, 0x10, 0x89.toByte(), 0x00, 0x00)

    //左节顺序
    val molLeftsectionOrder = byteArrayOf(0x00, 0x06, 0x10, 0x8A.toByte(), 0x00, 0x00)

    //右层数
    val molRightLayerCounts = byteArrayOf(0x00, 0x06, 0x10, 0x8B.toByte(), 0x00, 0x00)

    //右层顺序
    val molRightLayerOrder = byteArrayOf(0x00, 0x06, 0x10, 0x8C.toByte(), 0x00, 0x00)

    //右节数
    val molRightsectionCounts = byteArrayOf(0x00, 0x06, 0x10, 0x8D.toByte(), 0x00, 0x00)

    //右节顺序
    val molRightsectionOrder = byteArrayOf(0x00, 0x06, 0x10, 0x8E.toByte(), 0x00, 0x00)

    //灯所在面
    val molPositionLocationLight = byteArrayOf(0x00, 0x06, 0x10, 0x8F.toByte(), 0x00, 0x00)

    //节灯
    val molSectionLocateLight = byteArrayOf(0x00, 0x06, 0x10, 0x91.toByte(), 0x00, 0x00)

    //层灯
    val molLayerLocateLight = byteArrayOf(0x00, 0x06, 0x10, 0x90.toByte(), 0x00, 0x00)

    //定位灯测试
    val molLocateLightTest = byteArrayOf(0x00, 0x06, 0x10, 0x92.toByte(), 0x00, 0x00)

    //光照度
    val molLuminance = byteArrayOf(0x00, 0x06, 0x10, 0x52, 0x00, 0x00)

    //传感器
    val molSensorType = byteArrayOf(0x00, 0x06, 0x10, 0x53, 0x00, 0x00)

    //低速设定
    val molLowSpeed = byteArrayOf(0x00, 0x06, 0x10, 0x57, 0x00, 0x00)
    //中速设定
    val molMediumSpeed = byteArrayOf(0x00, 0x06, 0x10, 0x58, 0x00, 0x00)
    //高速设定
    val molHighSpeed = byteArrayOf(0x00, 0x06, 0x10, 0x59, 0x00, 0x00)
    //PM2.5限值1
    val molPm25Limit1 = byteArrayOf(0x00, 0x06, 0x10, 0x54, 0x00, 0x00)
    //PM2.5限值2
    val molPm25Limit2 = byteArrayOf(0x00, 0x06, 0x10, 0x55, 0x00, 0x00)
    //PM2.5限值3
    val molPm25Limit3 = byteArrayOf(0x00, 0x06, 0x10, 0x56, 0x00, 0x00)
    //是否边列
    val molIsEdge = byteArrayOf(0x00, 0x06, 0x10, 0x7B, 0x00, 0x00)

    //电机低速
    val molMachineLowSpeed = byteArrayOf(0x00, 0x06, 0x10, 0x7F, 0x00, 0x00)

    //电机高速
    val molMachineHighSpeed = byteArrayOf(0x00, 0x06, 0x10, 0x80.toByte(), 0x00, 0x00)

    //缓起时间 S
    val molDelayStart = byteArrayOf(0x00, 0x06, 0x10, 0x82.toByte(), 0x00, 0x00)

    //缓停时间
    val molDelayStop = byteArrayOf(0x00, 0x06, 0x10, 0x83.toByte(), 0x00, 0x00)

    //时间限值 最长移动时间
    val molMoveTimeLimit = byteArrayOf(0x00, 0x06, 0x10, 0x85.toByte(), 0x00, 0x00)

    //距离限值 最长移动距离
    val molMoveDistanceLimit = byteArrayOf(0x00, 0x06, 0x10, 0x86.toByte(), 0x00, 0x00)

    //电流限值 最大工作电流
    val molElectricityLimit = byteArrayOf(0x00, 0x06, 0x10, 0x81.toByte(), 0x00, 0x00)

    //过道距离
    val molSpaceBetween = byteArrayOf(0x00, 0x06, 0x10, 0x84.toByte(), 0x00, 0x00)

    //校准电机速度
    val molMachinePulse = byteArrayOf(0x00, 0x06, 0x10, 0x2D, 0x00, 0x00)

    //校准电机电流
    val molElectricCalibration = byteArrayOf(0x00, 0x06, 0x10, 0x2F, 0x00, 0x00)

    //电机方向
    val molElectricDirection = byteArrayOf(0x00, 0x06, 0x10, 0x7C, 0x00, 0x00)

    //压力开关
    val molPressureEnable = byteArrayOf(0x00, 0x06, 0x10, 0x70, 0x00, 0x00)

    //红外开关
    val molInfraredEnable = byteArrayOf(0x00, 0x06, 0x10, 0x71, 0x00, 0x00)

    //前入开关
    val molFrontEnterEnable = byteArrayOf(0x00, 0x06, 0x10, 0x72, 0x00, 0x00)

    //后入开关
    val molBackEnterEnable = byteArrayOf(0x00, 0x06, 0x10, 0x73, 0x00, 0x00)

    //自开开关
    val molAutoEnable = byteArrayOf(0x00, 0x06, 0x10, 0x74, 0x00, 0x00)

    //边门开关
    val molSideDoorEnable = byteArrayOf(0x00, 0x06, 0x10, 0x75, 0x00, 0x00)

    //手刹开关
    val molHandbrakeEnable = byteArrayOf(0x00, 0x06, 0x10, 0x76, 0x00, 0x00)

    //通道开关
    val molPassageEnable = byteArrayOf(0x00, 0x06, 0x10, 0x77, 0x00, 0x00)

    //开调开关
    val molOpenEnable = byteArrayOf(0x00, 0x06, 0x10, 0x78, 0x00, 0x00)

    //合调开关
    val molCloseEnable = byteArrayOf(0x00, 0x06, 0x10, 0x79, 0x00, 0x00)

    //光栅开关
    val molRasterEnable = byteArrayOf(0x00, 0x06, 0x10, 0x7A, 0x00, 0x00)

    //除尘开关
    val molCleanEnable = byteArrayOf(0x00, 0x06, 0x10, 0xA8.toByte(), 0x00, 0x00)
    //已移动距离
    val readMolMoveDistance = byteArrayOf(0x64, 0x04, 0x10, 0x07, 0x00, 0x01)

    //固定列除尘开关
    val gdlMolCleanEnable = byteArrayOf(0x64, 0x06, 0x10, 0x03, 0x00, 0x00)


    //净化状态105B-1064xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    private val readCleanStatus = byteArrayOf(0x64, 0x04, 0x10, 0x5B, 0x00, 0x0A)

    //密集架列状态
    private val readShelfStatus = byteArrayOf(0x64, 0x04, 0x10, 0x3C.toByte(), 0x00, 0x01)

    //密集架移动状态 bit0=1开架，bit1=1闭架，bit0=0，bit1=0停止
    private val readShelfMotionStatus = byteArrayOf(0x64, 0x04, 0x10, 0x15, 0x00, 0x01)

    //通风
    private val ventilate = byteArrayOf(0x64, 0x06, 0x10, 0x10, 0x00, 0x01)

    //停止通风
    private val stopVentilate = byteArrayOf(0x64, 0x06, 0x10, 0x10, 0x00, 0x00)

    //左区断电
    private val leftPowerOff = byteArrayOf(0x64, 0x06, 0x10, 0x6E.toByte(), 0x00, 0x00)

    //左区上电
    private val leftPowerOn = byteArrayOf(0x64, 0x06, 0x10, 0x6E.toByte(), 0x00, 0x01)

    //右区断电
    private val rightPowerOff = byteArrayOf(0x64, 0x06, 0x10, 0x6F.toByte(), 0x00, 0x00)

    //右区上电
    private val rightPowerOn = byteArrayOf(0x64, 0x06, 0x10, 0x6F.toByte(), 0x00, 0x01)

    //读取温湿度xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    private val readTempRH = byteArrayOf(0x64, 0x04, 0x10, 0x19, 0x00, 0x09)

    //开闭架  最后一位根据具体操作更改
    private val operateShelf = byteArrayOf(0x00, 0x06, 0x10, 0x02, 0x00, 0x00)

    //解锁  使用广发所有列同步解锁
    private val openLock = byteArrayOf(0x00, 0x06, 0x10, 0x01, 0x00, 0x00)

    //锁定  使用广发所有列同步锁定
    private val lock = byteArrayOf(0x00, 0x06, 0x10, 0x01, 0x00, 0x01)

    //查看物理锁定状态
    private val getLockStatus = byteArrayOf(0x64, 0x04, 0x10, 0x00, 0x00, 0x01)
    private val getLeftLockStatus = byteArrayOf(0x63, 0x04, 0x10, 0x00, 0x00, 0x01)
    private val getRightLockStatus = byteArrayOf(0x65, 0x04, 0x10, 0x00, 0x00, 0x01)

    //电机控制
    private val motorOperate = byteArrayOf(0x00, 0x06, 0x10, 0x02, 0x00, 0x00)

    //读取净化工作模式
    private val readCleanWorkType = byteArrayOf(0x00, 0x04, 0x10, 0xA0.toByte(), 0x00, 0x01)

    //写入净化工作模式
    private val writeCleanWorkType = byteArrayOf(0x00, 0x06, 0x10, 0xA0.toByte(), 0x00, 0x00)

    //写入传感器类型
    private val writeSensorType = byteArrayOf(0x64, 0x06, 0x10, 0x53, 0x00, 0x00)

    //写入除尘开关
    private val writeCleanStop = byteArrayOf(0x00, 0x06, 0x10, 0xA3.toByte(), 0x00, 0x00)

    //读取除尘模块
    private val readCleanModule = byteArrayOf(0x00, 0x04, 0x10, 0xA8.toByte(), 0x00, 0x01)

    //读取风速
    private val readWind = byteArrayOf(0x00, 0x04, 0x10, 0x22, 0x00, 0x01)

    //写入集控风速
    private val writeAllWind = byteArrayOf(0x64, 0x06, 0x10, 0xA2.toByte(), 0x00, 0x00)

    //写入单列风速
    private val writeSingleWind = byteArrayOf(0x00, 0x06, 0x10, 0xA1.toByte(), 0x00, 0x00)

    //读取滤网剩余用时
    private val readLvSurplusTime = byteArrayOf(0x64, 0x04, 0x10, 0xA3.toByte(), 0x00, 0x01)

    //读取滤网报警状态
    private val readLwAlarm = byteArrayOf(0x64, 0x04, 0x10, 0xA4.toByte(), 0x00, 0x01)

    //滤网复位
    private val writeLvReset = byteArrayOf(0x64, 0x06, 0x10, 0xA5.toByte(), 0x00, 0x01)

    //写入设计用时
    private val writeDesignTime = byteArrayOf(0x64, 0x06, 0x10, 0xA6.toByte(), 0x00, 0x00)

    //滤网计时开关
    private val writeLvOnoff = byteArrayOf(0x64, 0x06, 0x10, 0xA7.toByte(), 0x00, 0x00)


    //写入led显示 区号
    private val writeShelfArea = byteArrayOf(0x00, 0x06, 0x10, 0x62.toByte(), 0x00, 0x00)

    //写入led显示 列号
    private val writeShelfColumn = byteArrayOf(0x00, 0x06, 0x10, 0x63.toByte(), 0x00, 0x00)

    //写入led显示 节号
    private val writeShelfSection = byteArrayOf(0x00, 0x06, 0x10, 0x5B.toByte(), 0x00, 0x00)

    //写入led显示 层号
    private val writeShelfLayer = byteArrayOf(0x00, 0x06, 0x10, 0x5A.toByte(), 0x00, 0x00)

    //写入led显示 字符
    private val writeShelfCharacter = byteArrayOf(0x00, 0x06, 0x10, 0x5C.toByte(), 0x00, 0x00)
    //读右侧移动列开限1-16
    private val readRhtColumnOpenLimit = byteArrayOf(0x64, 0x04, 0x10, 0x2B, 0x00, 0x01)
    //读右侧移动列开限17-32
    private val readRhtColumnOpenLimitEnd = byteArrayOf(0x64, 0x04, 0x10, 0x2A, 0x00, 0x01)
    //读左侧移动列开限1-16
    private val readLftColumnOpenLimit = byteArrayOf(0x64, 0x04, 0x10, 0x2F, 0x00, 0x01)
    //读左侧移动列开限17-32
    private val readLftColumnOpenLimitEnd = byteArrayOf(0x64, 0x04, 0x10, 0x2E, 0x00, 0x01)
    //读右侧移动列合限1-16
    private val readRhtColumnCloseLimit = byteArrayOf(0x64, 0x04, 0x10, 0x2D, 0x00, 0x01)
    //读右侧移动列合限17-32
    private val readRhtColumnCloseLimitEnd = byteArrayOf(0x64, 0x04, 0x10, 0x2C, 0x00, 0x01)
    //读左侧移动列合限1-16
    private val readLftColumnCloseLimit = byteArrayOf(0x64, 0x04, 0x10, 0x30, 0x00, 0x01)
    //读左侧移动列合限17-32
    private val readLftColumnCloseLimitEnd = byteArrayOf(0x64, 0x04, 0x10, 0x31, 0x00, 0x01)
    //读通风
    private val readVentilateState = byteArrayOf(0x64, 0x04, 0x10, 0x50, 0x00, 0x01)
    //连读状态1101-110B
    private val readStatusFirst = byteArrayOf(0x64, 0x04, 0x11, 0x01, 0x00, 0x0B)
    private val readStatusSecond = byteArrayOf(0x64, 0x04, 0x11, 0x0C, 0x00, 0x0C)


    private lateinit var mShelfStatus: ShelfStatusBean
    private var mAir = Air()
    private lateinit var mFixColParams: FixColParamBean
    private lateinit var mShelfColumnStatus: ShelfColumnStatusBean
    private const val limitLength = 29
    private val list = ArrayList<Byte>()
    private var sleepCount = 1
    private var readLockCount = 1
    private var readShelfStatusCount = 1
    private var readColLimitCount = 1
    private var isLeftStart = false


    //消息队列
    private val messageQueue = mutableListOf<QueueMessage>()

    fun startRun(serialPort: String, baudRate: Int, isLeftStart: Boolean) {
        if (mySerialThread == null) {
            DEFAULT_SERIALPATH = serialPort
            DEFUALT_BAUDRATE = baudRate
            mySerialThread = MySerialThread(
                DEFAULT_SERIALPATH,
                DEFUALT_BAUDRATE
            )
            this.isLeftStart = isLeftStart
            mySerialThread!!.setIntervalTime(DEFAULT_SERIAL_INTERVAL_TIMES)
            mySerialThread!!.start()
        }

        mShelfStatus = ShelfStatusBean()
        mFixColParams = FixColParamBean()
        mShelfColumnStatus = ShelfColumnStatusBean()
    }

    fun stopRun() {
        if (mySerialThread != null) {
            mySerialThread!!.closeSerialPort()
            mySerialThread!!.setmRunflag(false)
            mySerialThread!!.interrupt()
            mySerialThread = null
        }
    }

    fun putMessage(message: ByteArray): Boolean {
        return try {
            when(byteArrToHexStr(byteArrayOf(message[1]))){
                "06"->{
                    when(byteArrToHexStr(byteArrayOf(message[2],message[3]))){
//                "1034" ->{
//                    if(byteArrToHexStr(byteArrayOf(message[5])).equals("00")){
//                        precedenceCommand(0, message)
//                    }else {
//                        actionFirstQueue(message)
//                    }
//                }
                        "106e",
                        "106f",
                        "10a0",
                        "1001",
                        "1010",
                        "10a1",
                        "10a2",
                        "108f",
                        "1090",
                        "1091",
                        "1092",->{
                            precedenceCommand(0, message)
                        }
                        "1002"->{
                            precedenceCommand(3, message)
                        }
                        "1007"->{
                            precedenceCommand(5, message)
                        }
                        //common params
                        "1052",
                        "1053",
                        "1057",
                        "1058",
                        "1059",
                        "1054",
                        "1055",
                        "1056",
                        "1050",
                        "107f",
                        "1080",
                        "1082",
                        "1083",
                        "1085",
                        "1086",
                        "1081",
                        "1084",
//                        "102d",
//                        "102f",
                        "107c",
                        "1070",
                        "1071",
                        "1072",
                        "1073",
                        "1074",
                        "1075",
                        "1076",
                        "1077",
                        "1078",
                        "1079",
                        "107a",
                        "10a8",
                        "1087",
                        "1088",
                        "1089",
                        "108a",
                        "108b",
                        "108c",
                        "108d",
                        "108e",
                        "100c",
                        "100d",
                        "106b",->{
                            precedenceCommand(4, message)
                        }
                        else ->{
                            val queueMessage = QueueMessage("${System.currentTimeMillis()}-${byteArrToHexStr(
                                byteArrayOf(message[2],message[3]))}-${byteArrToHexStr(byteArrayOf(message[0]))}-${byteArrToHexStr(byteArrayOf(message[1]))}",message)
                            messageQueue.add(queueMessage)
                        }
                    }
                }
                else ->{
                    val queueMessage = QueueMessage("${System.currentTimeMillis()}-${byteArrToHexStr(
                        byteArrayOf(message[2],message[3]))}-${byteArrToHexStr(byteArrayOf(message[0]))}-${byteArrToHexStr(byteArrayOf(message[1]))}",message)
                    messageQueue.add(queueMessage)
                }
            }
            true
        } catch (e: java.lang.Exception) {
            Log.d("test", "====添加消息队列出错======" + e.printStackTrace())
            false
        }

    }

    private fun precedenceCommand(index: Int, message: ByteArray) {
        val queueMessage = QueueMessage(
            "${System.currentTimeMillis()}-${
                byteArrToHexStr(
                    byteArrayOf(message[2], message[3])
                )
            }-${byteArrToHexStr(byteArrayOf(message[0]))}-${byteArrToHexStr(byteArrayOf(message[1]))}",
            message
        )
        if(messageQueue.size == 0){
            messageQueue.add(0, queueMessage)
            return
        }else if(index >= messageQueue.size){
            messageQueue.add(queueMessage)
        }else{
            messageQueue.add(index, queueMessage)
        }
    }

    private fun actionFirstQueue(message: ByteArray) {
        val openLockByteArray = makeOrderBytes(openLock)
        val unlockQueueMessage = QueueMessage("${System.currentTimeMillis()}-${byteArrToHexStr(
            byteArrayOf(openLockByteArray[2],openLockByteArray[3]))}-${byteArrToHexStr(byteArrayOf(openLockByteArray[0]))}-${byteArrToHexStr(byteArrayOf(openLockByteArray[1]))}",openLockByteArray)
        val queueMessage = QueueMessage("${System.currentTimeMillis()}-${byteArrToHexStr(
            byteArrayOf(message[2],message[3]))}-${byteArrToHexStr(byteArrayOf(message[0]))}-${byteArrToHexStr(byteArrayOf(message[1]))}",message)
        messageQueue.clear()
        messageQueue.add(unlockQueueMessage)
        messageQueue.add(queueMessage)
    }

    private class MySerialThread(serialpath: String?, baudrate: Int) :
        SerialBaseThread(serialpath, baudrate) {
        override fun run() {
            try {
                openSerialPort()
//                putMessage(makeOrderBytes(readTempRH))
                SuperLiveDataManager.serialStart.postOneOff(true)
                while (true) {
                    try {
//                        SuperLiveDataManager.voiceCommandFormDev.observeOneOff()
                        if (isWrite) {
                            if (repeatCount == 0) {
                                writeData(currentMessage.message)
                                repeatCount++
                                Log.d("test", "重新发送一次消息")
                            } else {
                                repeatCount = 0
                                isWrite = false
                                Log.d("test", "重试失败，发送下一条消息")
                            }
                        }
                        if (messageQueue.size >0 && !isWrite){
                                val queueMessage = messageQueue.removeAt(0)
                                if(queueMessage.message.isNotEmpty()){
                                    val messageContent = byteArrToHexStr(queueMessage.message)
                                    if(isShowLog){
                                        Log.d("test","====取出消息====${queueMessage.messageTitle}===${ messageContent}")
                                    }
                                    if (queueMessage.message[2]== 0x10.toByte() && queueMessage.message[3] == 0x02.toByte()){
                                        //开闭架命令
                                        operateType = queueMessage.message[5].toInt()
                                        operateAddress = queueMessage.message[0]
                                        sendOperateOrder = true
                                    }
                                    isWrite = writeData(queueMessage.message)
                                    if (lastAddress != 0.toByte() && queueMessage.message[0] == lastAddress){
                                        SuperLiveDataManager.writeLedComplete.postOneOff("")
                                        lastAddress = 0
                                    }
                                    currentMessage = queueMessage
                                    if (isDebug && isWrite){
                                        if (isOnlyControl){
                                            val messageList = currentMessage.messageTitle.split("-")
                                            val isRead = messageList[3] == "04"
                                            if (!isRead){
                                                val serialData = OrderBean(0,TimeUtils.getyMdHms(),messageContent?:"")
                                                SuperLiveDataManager.serialData.postOneOff(serialData)
                                            }
                                        }else{
                                            val serialData = OrderBean(0,TimeUtils.getyMdHms(),messageContent?:"")
                                            SuperLiveDataManager.serialData.postOneOff(serialData)
                                        }

                                    }
                                }
                        }

//                        if (sleepCount >= 10) {
//                            //添加空气质量数据
//                            putMessage(makeOrderBytes(readTempRH))
//                            sleepCount = 0
//                        }
                        if (readLockCount >= 8){
                            //读取锁定状态
//                            if (isLeftStart){
//                                putMessage(makeOrderBytes(getRightLockStatus))
//                            }else{
//                                putMessage(makeOrderBytes(getLeftLockStatus))
//                            }

//                            putMessage(makeOrderBytes(getLockStatus))
                            if(isShowLog){
                                Log.d("test", "messageQueue capacity: ${messageQueue.size}")
                            }
                            putMessage(makeOrderBytes(readStatusFirst))
                            putMessage(makeOrderBytes(readStatusSecond))
                            readLockCount = 0
                        }
//                        if(readShelfStatusCount >= 5){
//                            putMessage(makeOrderBytes(readShelfStatus))
//                            putMessage(makeOrderBytes(readShelfMotionStatus))
//                            putMessage(makeOrderBytes(readVentilateState))
//                            putMessage(makeOrderBytes(readRhtColumnCloseLimit))
//                            putMessage(makeOrderBytes(readRhtColumnCloseLimitEnd))
//                            putMessage(makeOrderBytes(readLftColumnCloseLimit))
//                            putMessage(makeOrderBytes(readLftColumnCloseLimitEnd))
//                            readShelfStatusCount = 0
//                        }
//                        if(readColLimitCount >= 5){
//                            putMessage(makeOrderBytes(readRhtColumnOpenLimit))
//                            putMessage(makeOrderBytes(readRhtColumnOpenLimitEnd))
//                            putMessage(makeOrderBytes(readLftColumnOpenLimit))
//                            putMessage(makeOrderBytes(readLftColumnOpenLimitEnd))
//                            putMessage(makeOrderBytes(readRhtColumnCloseLimit))
//                            putMessage(makeOrderBytes(readRhtColumnCloseLimitEnd))
//                            putMessage(makeOrderBytes(readLftColumnCloseLimit))
//                            putMessage(makeOrderBytes(readLftColumnCloseLimitEnd))
//                        }
                        if(messageQueue.size>Short.MAX_VALUE){
                            messageQueue.clear()
                        }
                        readLockCount++
//                        sleepCount++
//                        readShelfStatusCount++
//                        readColLimitCount++

                        sleep(200)

//
                    } catch (e: InterruptedException) {
                        e.printStackTrace()
                    }
                }
            } catch (e: SecurityException) {
                e.printStackTrace()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        } // run()

        override fun onDataReceived(buffer: ByteArray, size: Int) {
            val newBuffer = ByteArray(size)
            System.arraycopy(buffer, 0, newBuffer, 0, size)
            val messageContent = byteArrToHexStr(newBuffer)
            if(isShowLog){
                Log.d("test","===收到串口消息====${messageContent}====messageTitle===${currentMessage.messageTitle}")
            }
            val messageList = currentMessage.messageTitle.split("-")
            val isRead = messageList[3] == "04"
            isWrite = false

            if (isDebug) {
                if (!isOnlyControl || !isRead) {
                    val serialData = OrderBean(1, TimeUtils.getyMdHms(), messageContent ?: "")
                    SuperLiveDataManager.serialData.postOneOff(serialData)
                }
            }

            try {
                val fromMol = messageContent!!.substring(2,4)
                if(fromMol == "03"){
                    val currentTime = System.currentTimeMillis()
                    if ( currentTime - lastOrderTime < 2000 ){
                        return
                    }
                    lastOrderTime = currentTime
                    val molCmd = messageContent.substring(4,8)
                    currentShelfAddr = buffer[0]
                    val contentCnt = messageContent.substring(8,12).toInt(16)

                    Log.d("test", "contentCnt: $contentCnt")
                    var tmpSearchContent = ""
                    when(molCmd){
                        "3000" ->{
                            tmpSearchContent = messageContent.substring(12, messageContent.indexOf("ffff"))
                            val strKeyWord = hexToString(tmpSearchContent)
                            Log.d("test", "mol search content: $tmpSearchContent and Chinese: $strKeyWord")
                            SuperLiveDataManager.molSearchText.postOneOff(strKeyWord)
                        }
                        "10af" ->{
                            //下一条
                            SuperLiveDataManager.shelfSearchOprate.postOneOff(1)
                        }
                        "10b0" ->{
                            //上一条
                            SuperLiveDataManager.shelfSearchOprate.postOneOff(2)
                        }
                        "10b1" ->{
                            //开架
                            SuperLiveDataManager.shelfSearchOprate.postOneOff(3)
                        }
                    }
                }else{
                    when (messageList[1]) {
                        "106a" -> {
                            //固定列参数
                            if (isRead){
                                mFixColParams.rhtColCounts = byteArrToInt(buffer[3], buffer[4])
                                mFixColParams.lftColCounts = byteArrToInt(buffer[5], buffer[6])
                            }

                        }

                        "1034" -> {
                            //开闭架或左右移
                        }

                        "1029" -> {
                            //解锁
                        }

                        "1027" -> {
                            //移动状态
//                        val motionStatus = byteArrToInt(buffer[3], buffer[4])
//                        SuperLiveDataManager.shelfMotionState.postOneOff(motionStatus)
                        }

                        "1028" -> {
                            //锁定状态
//                       val lockStatus = byteArrToInt(buffer[3],buffer[4])
//                        Log.d("test","======收到锁定状态回复========${byteArrToHexStr(buffer)}====当前状态===${lockStatus}")
//                        SuperLiveDataManager.getLockStatus.postOneOff(lockStatus)
//                        mShelfStatus.isLock = lockStatus.toString()
                        }
                        "105b" ->{
                            //净化工作模式
//                        if (isRead){
//                            val type = byteArrToInt(buffer[3],buffer[4])
//                            mShelfStatus.workMode = type.toString()
//                            //净化风速
//                            val windSpeed = byteArrToInt(buffer[21],buffer[22])
//                            mShelfStatus.windSpeed = windSpeed.toString()
//                            if (messageList[2] == "64"){
//                                SuperLiveDataManager.areaCleanType.postOneOff(type)
//                                SuperLiveDataManager.areaWind.postOneOff(windSpeed)
//                                SuperLiveDataManager.shelfWind.postOneOff(windSpeed)
//                                if(type == 2){
//                                    SuperLiveDataManager.shelfCleanType.postOneOff(0)
//                                }else{
//                                    SuperLiveDataManager.shelfCleanType.postOneOff(1)
//                                }
//                            }else{
//                                SuperLiveDataManager.shelfCleanType.postOneOff(type)
//                                SuperLiveDataManager.shelfWind.postOneOff(windSpeed)
//                            }
//                        }

                        }
                        "1064" ->{
                            //净化风速
//                        val type = byteArrToInt(buffer[3],buffer[4])
//                        if (messageList[2] == "64"){
//                            mShelfStatus.windSpeed = type.toString()
//                        }
                            //                    else{
                            //                        SuperLiveDataManager.shelfWind.postOneOff(type)
                            //                    }
                        }
                        "10a8" ->{
                            //除尘使能
                            val type = byteArrToInt(buffer[3],buffer[4])
                            if (messageList[2] == "64"){
                                SuperLiveDataManager.areaHaveClean.postOneOff(type)
                            }else{
                                SuperLiveDataManager.shelfHaveClean.postOneOff(type)
                            }
                        }

                        "10a3" -> {
                            FileUtils.saveToFile(
                                "/readMsg.txt",
                                "读到滤网剩余用时：" + hexToString(buffer)
                            )
                            //滤网剩余用时
                            val time = byteArrToInt(buffer[3], buffer[4])
                            SuperLiveDataManager.filterSurplusTime.postOneOff(time)
                        }

                        "10a4" -> {
                            FileUtils.saveToFile(
                                "/readMsg.txt",
                                "读到滤网报警状态：" + hexToString(buffer)
                            )
                            //滤网报警状态
                            val alarm = byteArrToInt(buffer[3], buffer[4])
                            SuperLiveDataManager.filterIsAlarm.postOneOff(alarm)
                        }

//                    "1052" -> {
//                        //删除上一个
//                        AirRepository.deleteAirAll()
//
//                        //9个空气质量数据 两个byte表示一个
//                        mAir.temp = byteArrToInt(buffer[3],buffer[4]).toString()
//                        mAir.rh = byteArrToInt(buffer[5],buffer[6]).toString()
//                        mAir.co2 = byteArrToInt(buffer[7],buffer[8]).toString()
//                        mAir.pm2_5 = byteArrToInt(buffer[9],buffer[10]).toString()
//                        mAir.pm1 = byteArrToInt(buffer[11],buffer[12]).toString()
//                        mAir.pm10 = byteArrToInt(buffer[13],buffer[14]).toString()
//                        mAir.tvoc = byteArrToInt(buffer[15],buffer[16]).toString()
//                        mAir.hcho = byteArrToInt(buffer[17],buffer[18]).toString()
//                        mAir.so2 = byteArrToInt(buffer[19],buffer[20]).toString()
//                        mAir.time = System.currentTimeMillis()
//                        Log.d("test","====插入新的空气数据=====${mAir.time}")
//                        syncLock.lock()
//                        AirRepository.insertAir(mAir)
//                        syncLock.unlock()
//                     }
                        "1096" -> {
                            //密集架状态
//                        mShelfStatus.status = byteArrToInt(buffer[3],buffer[4]).toString()
                            //Log.d("test", "====密集架状态===${mShelfStatus.status}")
                        }
//                    "102b" ->{
//                        ShelfColDetailStatusBean.rhtOpenLimit = convertToBinaryStringAndReverse(byteArrToInt(buffer[3],buffer[4]))
//                    }
//                    "102a" ->{
//                        ShelfColDetailStatusBean.rhtOpenLimitEnd = convertToBinaryStringAndReverse(byteArrToInt(buffer[3],buffer[4]))
//                    }
//                    "102f" ->{
//                        ShelfColDetailStatusBean.lftOpenLimit = convertToBinaryStringAndReverse(byteArrToInt(buffer[3],buffer[4]))
//                    }
//                    "102e" ->{
//                        ShelfColDetailStatusBean.lftOpenLimitEnd = convertToBinaryStringAndReverse(byteArrToInt(buffer[3],buffer[4]))
//                    }
//                    "102d" ->{
//                        ShelfColDetailStatusBean.rhtCloseLimit = convertToBinaryStringAndReverse(byteArrToInt(buffer[3],buffer[4]))
//                    }
//                    "102c" ->{
//                        ShelfColDetailStatusBean.rhtCloseLimitEnd = convertToBinaryStringAndReverse(byteArrToInt(buffer[3],buffer[4]))
//                    }
//                    "1031" ->{
//                        ShelfColDetailStatusBean.lftCloseLimit = convertToBinaryStringAndReverse(byteArrToInt(buffer[3],buffer[4]))
//                    }
//                    "1030" ->{
//                        ShelfColDetailStatusBean.lftCloseLimitEnd = convertToBinaryStringAndReverse(byteArrToInt(buffer[3],buffer[4]))
//                    }
                        "1007" ->{
                            ShelfMoveDistanceStatusBean.colMoveDistance = byteArrToInt(buffer[3],buffer[4]).toString()
                        }
//                    "1050" ->{
//                        val ventilateState = byteArrToInt(buffer[3],buffer[4])
//                        SuperLiveDataManager.shelfVentilateState.postOneOff(ventilateState)
//                    }
                    "1101" -> {
                        if(messageContent?.startsWith("640416") == true){
                            //移动状态
                            val motionStatus = byteArrToInt(buffer[3],buffer[4])
                            if (lastMotionStatus != motionStatus){
                                if(isShowLog){
                                    Log.d("test","======移动状态========${motionStatus}==============")
                                }
                                SuperLiveDataManager.shelfMotionState.postOneOff(motionStatus)
                                SuperLiveDataManager.motionState.postOneOff(motionStatus)
                                lastMotionStatus = motionStatus
                            }

                            VoiceSerialBean.motionStatus = motionStatus

                            //锁定状态
                            lockStatus = byteArrToInt(buffer[5],buffer[6])
                            if(isShowLog){
                                Log.d("test","======收到锁定状态回复========${byteArrToHexStr(buffer)}====当前状态===${lockStatus}")
                            }
                            SuperLiveDataManager.getLockStatus.postOneOff(lockStatus)
                            mShelfStatus.isLock = lockStatus.toString()

                            ShelfColDetailStatusBean.rhtOpenLimitEnd = convertToBinaryStringAndReverse(byteArrToInt(buffer[7],buffer[8]))
                            ShelfColDetailStatusBean.rhtOpenLimit = convertToBinaryStringAndReverse(byteArrToInt(buffer[9],buffer[10]))
                            ShelfColDetailStatusBean.rhtCloseLimitEnd = convertToBinaryStringAndReverse(byteArrToInt(buffer[11],buffer[12]))
                            ShelfColDetailStatusBean.rhtCloseLimit = convertToBinaryStringAndReverse(byteArrToInt(buffer[13],buffer[14]))
                            ShelfColDetailStatusBean.lftOpenLimitEnd = convertToBinaryStringAndReverse(byteArrToInt(buffer[15],buffer[16]))
                            ShelfColDetailStatusBean.lftOpenLimit = convertToBinaryStringAndReverse(byteArrToInt(buffer[17],buffer[18]))
                            ShelfColDetailStatusBean.lftCloseLimitEnd = convertToBinaryStringAndReverse(byteArrToInt(buffer[19],buffer[20]))
                            ShelfColDetailStatusBean.lftCloseLimit = convertToBinaryStringAndReverse(byteArrToInt(buffer[21],buffer[22]))

                            val ventilateState = byteArrToInt(buffer[23],buffer[24])
                            SuperLiveDataManager.shelfVentilateState.postOneOff(ventilateState)
                        }
                    }
                    "110c" ->{
                        if(messageContent?.startsWith("640418") == true){
                            //删除上一个
                            AirRepository.deleteAirAll()

                            //9个空气质量数据 两个byte表示一个
                            mAir.temp = byteArrToInt(buffer[3],buffer[4]).toString()
                            mAir.rh = byteArrToInt(buffer[5],buffer[6]).toString()
                            mAir.co2 = byteArrToInt(buffer[7],buffer[8]).toString()
                            mAir.pm2_5 = byteArrToInt(buffer[9],buffer[10]).toString()
                            mAir.pm1 = byteArrToInt(buffer[11],buffer[12]).toString()
                            mAir.pm10 = byteArrToInt(buffer[13],buffer[14]).toString()
                            mAir.tvoc = byteArrToInt(buffer[15],buffer[16]).toString()
                            mAir.hcho = byteArrToInt(buffer[17],buffer[18]).toString()
                            mAir.so2 = byteArrToInt(buffer[19],buffer[20]).toString()
                            mAir.time = System.currentTimeMillis()
                            if(isShowLog){
                                Log.d("test","====插入新的空气数据=====${mAir.time}")
                            }
                            AirRepository.insertAir(mAir)

                            //净化工作模式
                            if (isRead){
                                val type = byteArrToInt(buffer[21],buffer[22])
                                mShelfStatus.workMode = type.toString()
                                //净化风速
                                val windSpeed = byteArrToInt(buffer[23],buffer[24])
                                mShelfStatus.windSpeed = windSpeed.toString()
                                SuperLiveDataManager.areaCleanType.postOneOff(type)
                                SuperLiveDataManager.areaWind.postOneOff(windSpeed)
                                SuperLiveDataManager.shelfWind.postOneOff(windSpeed)
                            }

                            //密集架状态
                            mShelfStatus.status = byteArrToInt(buffer[25],buffer[26]).toString()
                        }

                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("test", "onDataReceived: ${e.message}", )
            }


        }
    }

    fun getFixColParams(): FixColParamBean{
        return mFixColParams
    }
    /**
     *读取参数
     */
    fun readFixColParams(): Boolean{
        val result = makeOrderBytes(fixColumnParams)
        return putMessage(result)
    }

    /**
     * 左列数
     */
    fun lftColumnCounts(count: Int): Boolean{
        lftColumnCounts[5] = count.toByte()
        val result = makeOrderBytes(lftColumnCounts)
        return putMessage(result)
    }

    /**
     * 右列数
     */
    fun rhtColumnCounts(count: Int): Boolean{
        rhtColumnCounts[5] = count.toByte()
        val result = makeOrderBytes(rhtColumnCounts)
        return putMessage(result)
    }

    /**
     * 打开指定定位灯
     */
    fun openSpecficLocateLight(address: Byte, position: Byte, section: Byte, layer: Byte): Boolean{
        molPositionLocationLight[0] = address
        molPositionLocationLight[5] = position
        val positionLight = makeOrderBytes(molPositionLocationLight)
        molLayerLocateLight[0] = address
        molLayerLocateLight[5] = layer
        val layerLight = makeOrderBytes(molLayerLocateLight)
        molSectionLocateLight[0] = address
        molSectionLocateLight[5] = section
        val secLight = makeOrderBytes(molSectionLocateLight)
        return putMessage(positionLight) && putMessage(layerLight) && putMessage(secLight)
    }

    /**
     * 定位灯测试
     */
    fun locateLightTest(content:Byte): Boolean{
        molLocateLightTest[5] = content
        val content = makeOrderBytes(molLocateLightTest)
        return putMessage(content)
    }

    /**
     * 融合屏LED字符下写
     */
    fun writeFusionLed(address: Byte, position: Byte, content:Byte): Boolean{
        writeShelfCharacter[0] = address
        writeShelfCharacter[3] = position
        writeShelfCharacter[5] =  content
        val content = makeOrderBytes(writeShelfCharacter)
        return  putMessage(content)
    }

    fun getShelfStatus(): ShelfStatusBean{
        return  mShelfStatus
    }

    /**
     * 获取空气质量信息
     */
    fun getAir(): Air {
        return mAir
    }

    /**
     * 移动列通用下发
     */
    fun molGeneralCommand(orderBytes: ByteArray):Boolean {
        val result = makeOrderBytes(orderBytes)
        return putMessage(result)
    }

    /**
     * 通风
     */
    fun ventilate():Boolean{
        val result = makeOrderBytes(ventilate)
        return  putMessage(result)
    }
    /**
     * 停止通风
     */
    fun stopVentilate():Boolean{
        val result = makeOrderBytes(stopVentilate)
        return  putMessage(result)
    }
    /**
     * 左区断电
     */
    fun leftPowerOff():Boolean{
        val result = makeOrderBytes(leftPowerOff)
        return  putMessage(result)
    }
    /**
     * 左区上电
     */
    fun leftPowerOn():Boolean{
        val result = makeOrderBytes(leftPowerOn)
        return  putMessage(result)
    }
    /**
     * 右区断电
     */
    fun rightPowerOff():Boolean{
        val result = makeOrderBytes(rightPowerOff)
        return  putMessage(result)
    }
    /**
     * 右区上电
     */
    fun rightPowerOn():Boolean{
        val result = makeOrderBytes(rightPowerOn)
        return  putMessage(result)
    }

    /**
     * 开架1 闭架2 闭架all3
     */
    fun operateShelf(address: Byte,  byte: Byte):Boolean{
        operateShelf[0] = address
        operateShelf[5] = byte
        val result = makeOrderBytes(operateShelf)
        Log.d("test","===写入开闭架命令===${byteArrayToString(result, result.size,0)}")
        if (lockStatus != 0 ){
            Log.d("test","===开闭架增加解锁===")
            openLock()
        }
        return  putMessage(result)
    }

    /**
     * 停止
     */
    fun stopShelf(address: Byte):Boolean{
        operateShelf[0] = address
        operateShelf[5] = 0x00
        val result = makeOrderBytes(operateShelf)
        Log.d("test","===写入停止命令===${byteArrayToString(result, result.size,0)}")
        return  putMessage(result)
    }

    /**
     * 停止
     */
    fun stopAllShelf():Boolean{
        if(isLeftStart) operateShelf[0] = 0x65 else  operateShelf[0] = 0x63
        operateShelf[5] = 0x00
        Log.d("test","===写入停止命令===${byteArrayToString(operateShelf, operateShelf.size,0)}")
        val result = makeOrderBytes(operateShelf)
        return  putMessage(result)
    }
    /**
     * 解锁
     */
    fun openLock(): Boolean {
        val result = makeOrderBytes(openLock)
        return  putMessage(result)
    }
    /**
     * 锁定
     */
    fun lock(): Boolean {
        val result = makeOrderBytes(lock)
        return  putMessage(result)
    }
    /**
     * 获取锁定状态
     */
    fun getLockStatus(address: Byte): Boolean {
        getLockStatus[0] = address
        val result = makeOrderBytes(getLockStatus)
        return  putMessage(result)
    }
    /**
     * 左移右移
     */
    fun operateMotor(address: Byte, isLeftMove:Boolean, isLeft:Boolean):Boolean{
        motorOperate[0] = address
        if (isLeftMove){
            motorOperate[5] = if (isLeft) 0x01 else 0x02
        }else{
            motorOperate[5] = if (isLeft) 0x02 else 0x01
        }
        val result = makeOrderBytes(motorOperate)
        Log.d("test","===写入左右移动数据==是否左移==${isLeftMove}==是否左侧==${isLeft}=${byteArrayToString(result, result.size,0)}")
        return  putMessage(result)

    }

    /**
     * 读取105b-1064，净化状态
     */
    fun readCleanStatus(address: Byte): Boolean{
        readCleanStatus[0] = address
        val result = makeOrderBytes(readCleanStatus)
        Log.d("test=====", "readCleanStatus: "+ byteArrToHexStr(makeOrderBytes(readCleanStatus)))
        return putMessage(result)
    }

    /**
     * 读取净化工作模式
     */
    fun readCleanWorkType(address: Byte): Boolean{
        readCleanWorkType[0] = address
        val result = makeOrderBytes(readCleanWorkType)
        return  putMessage(result)
    }
    /**
     * 写入净化工作模式
     */
    fun writeCleanWorkType(address: Byte, type:Byte): Boolean{
        writeCleanWorkType[0] = address
        writeCleanWorkType[5] = type
        val result = makeOrderBytes(writeCleanWorkType)
        return  putMessage(result)
    }
    /**
     * 读取净化风速
     */
    fun readWind(address: Byte): Boolean{
        readWind[0] = address
        val result = makeOrderBytes(readWind)
        return  putMessage(result)
    }
    /**
     * 写入集控风速
     */
    fun writeAllWind( type:Byte): Boolean{
        writeAllWind[5] = type
        val result = makeOrderBytes(writeAllWind)
        return  putMessage(result)
    }
    /**
     * 写入手动风速
     */
    fun writeSingleWind(address: Byte, type:Byte): Boolean{
        Log.d("test","====写入手动风速====")
        writeSingleWind[0] = address
        writeSingleWind[5] = type
        val result = makeOrderBytes(writeSingleWind)
        return putMessage(result)
    }


    /**
     * 滤网复位
     */
    fun writeLwReset(): Boolean {
        val result = makeOrderBytes(writeLvReset)
        FileUtils.saveToFile("/readMsg.txt", "滤网复位：" + hexToString(result))
        return putMessage(result)
    }

    /**
     * 滤网设计用时
     */
    fun writeDesignTime(time: Int): Boolean {
        val bytes = intTo2Bytes(time);
        writeDesignTime[4] = bytes[1]
        writeDesignTime[5] = bytes[0]
        val result = makeOrderBytes(writeDesignTime)
        FileUtils.saveToFile("/readMsg.txt", "滤网设计用时：" + hexToString(result))
        return putMessage(result)
    }

    /**
     * 计时开关
     */
    fun writeLwOnoff(boolean: Boolean): Boolean {
        if (boolean) {
            writeLvOnoff[5] = 0x01//0x00不计时 0x01计时
        } else {
            writeLvOnoff[5] = 0x00//0x00不计时 0x01计时
        }

        val result = makeOrderBytes(writeLvOnoff)
        FileUtils.saveToFile("/readMsg.txt", "滤网计时开关：" + hexToString(result))
        return putMessage(result)
    }


    /**
     * 固定列除尘使能开关
     */
    fun writeCCSNOnoff(boolean: Boolean): Boolean {
        if (boolean) {
            gdlMolCleanEnable[5] = 0x01//0x00不使能 0x01使能
        } else {
            gdlMolCleanEnable[5] = 0x00
        }

        val result = makeOrderBytes(gdlMolCleanEnable)
        FileUtils.saveToFile("/readMsg.txt", "固定列除尘使能开关：" + hexToString(result))
        return putMessage(result)
    }


    /**
     * 读取滤网剩余时间
     */
    fun readLwSurplus(): Boolean {
        val result = makeOrderBytes(readLvSurplusTime)
        FileUtils.saveToFile("/readMsg.txt", "读取滤网剩余时间：" + hexToString(result))
        return putMessage(result)
    }

    /**
     * 读取滤网报警状态
     */
    fun readLwAlarm(): Boolean {
        val result = makeOrderBytes(readLwAlarm)
        FileUtils.saveToFile("/readMsg.txt", "读取滤网报警状态：" + hexToString(result))
        return putMessage(result)
    }


    /**
     * 读取除尘使能
     */
    fun readHaveClean(address: Byte): Boolean{
        readCleanModule[0] = address
        val result = makeOrderBytes(readCleanModule)
        return  putMessage(result)
    }

    /**
     * 写入集控停止
     */
    fun writeCleanStop( type:Byte): Boolean{
        writeCleanStop[5] = type
        val result = makeOrderBytes(writeCleanStop)
        return  putMessage(result)
    }

    /**
     * 写入传感器类型
     */
    fun writeSensorType( type:Byte): Boolean{
        writeSensorType[5] = type
        val result = makeOrderBytes(writeSensorType)
        return  putMessage(result)
    }
    /**
     * 写入led显示 区号 列号
     */
    fun writeLed(address: Byte, area:Byte, column:Byte): Boolean{
        Log.d("test","===========添加led显示命令===address==${address}===column==${column}===")
        writeShelfArea[0] = address
        writeShelfArea[5] =  area
        writeShelfColumn[0] = address
        writeShelfColumn[5] =  column
        val areaOrder = makeOrderBytes(writeShelfArea)
        val columnOrder = makeOrderBytes(writeShelfColumn)
        return  putMessage(areaOrder) && putMessage(columnOrder)
    }
    fun writeLed(address: Byte, area:Byte, column:Byte, section:Byte, layer:Byte): Boolean{
        writeShelfArea[0] = address
        writeShelfArea[5] =  area
        writeShelfColumn[0] = address
        writeShelfColumn[5] =  column
        writeShelfSection[0] = address
        writeShelfSection[5] = section
        writeShelfLayer[0] = address
        writeShelfLayer[5] = layer
        val areaOrder = makeOrderBytes(writeShelfArea)
        val columnOrder = makeOrderBytes(writeShelfColumn)
        val sectionOrder = makeOrderBytes(writeShelfColumn)
        val layerOrder = makeOrderBytes(writeShelfColumn)
        return  putMessage(areaOrder) && putMessage(columnOrder) && putMessage(sectionOrder) && putMessage(layerOrder)
    }

    /**
     * 返回一条移动列查询结果
     */
    fun writeSearchResult(searchResultBean: ShelfSearchResultBean,size: Int,index: Int){
        //下发查询结果
        val nameString = searchResultBean.archiveName?:""
        val numberString = searchResultBean.boxNumber?:""
        val positionString = searchResultBean.positionDes?:""
        val indexString = "当前第${index}条    共${size}条"
        val nameBytes = nameString.toByteArray(Charset.forName("GBK"))
        val numberBytes = numberString.toByteArray(Charset.forName("GBK"))
        val positionBytes = positionString.toByteArray(Charset.forName("GBK"))
        val indexBytes = indexString.toByteArray(Charset.forName("GBK"))
        val writeSearchName = byteArrayOf(currentShelfAddr, 0x03, 0x30, 0x10,0x00)   //档案名称
        val writeSearchNumber = byteArrayOf(currentShelfAddr, 0x03, 0x30, 0x30,0x00) //档案编号
        val writeSearchPosition = byteArrayOf(currentShelfAddr, 0x03, 0x30, 0x60,0x00) //档案位置
        val writeSearchIndex = byteArrayOf(currentShelfAddr, 0x03, 0x30, 0x50,0x00) //档案个数
        if (nameString.length <= 16){
            val writeSearchNameOrder = mutableListOf<Byte>()
            writeSearchNameOrder.addAll(writeSearchName.toList())
            writeSearchNameOrder.add((nameString.length+1).toByte())
            writeSearchNameOrder.addAll(nameBytes.toList())
            writeSearchNameOrder.add((0xff).toByte())
            writeSearchNameOrder.add((0xff).toByte())
            putMessage(makeOrderBytes(writeSearchNameOrder.toByteArray()))
        }else{
            Log.d("test","======名称超过16个字=======")
        }
        if (numberString.length <= 16){
            val writeSearchNumberOrder = mutableListOf<Byte>()
            writeSearchNumberOrder.addAll(writeSearchNumber.toList())
            writeSearchNumberOrder.add((numberString.length+1).toByte())
            writeSearchNumberOrder.addAll(numberBytes.toList())
            writeSearchNumberOrder.add((0xff).toByte())
            writeSearchNumberOrder.add((0xff).toByte())
            putMessage(makeOrderBytes(writeSearchNumberOrder.toByteArray()))
        }else{
            Log.d("test","======盒号超过16个字=======")
        }
        if (positionString.length <= 16){
            val writeSearchPositionOrder = mutableListOf<Byte>()
            writeSearchPositionOrder.addAll(writeSearchPosition.toList())
            writeSearchPositionOrder.add((positionString.length+1).toByte())
            writeSearchPositionOrder.addAll(positionBytes.toList())
            writeSearchPositionOrder.add((0xff).toByte())
            writeSearchPositionOrder.add((0xff).toByte())
            putMessage(makeOrderBytes(writeSearchPositionOrder.toByteArray()))
        }else{
            Log.d("test","======位置超过16个字=======")
        }

        val writeSearchIndexOrder = mutableListOf<Byte>()
        writeSearchIndexOrder.addAll(writeSearchIndex.toList())
        writeSearchIndexOrder.add((indexString.length+1).toByte())
        writeSearchIndexOrder.addAll(indexBytes.toList())
        writeSearchIndexOrder.add((0xff).toByte())
        writeSearchIndexOrder.add((0xff).toByte())
        putMessage(makeOrderBytes(writeSearchIndexOrder.toByteArray()))


    }



    /**
     * 组成命令
     * @param orderBytes
     * @return
     */
    private fun makeOrderBytes(orderBytes: ByteArray): ByteArray {
        val i = CRC16Util.calcCrc16(orderBytes)
        val crc16 = CRC16Util.getCrc(i)
        val writeBytes = ByteArray(orderBytes.size + 2)
        System.arraycopy(orderBytes, 0, writeBytes, 0, orderBytes.size)
        System.arraycopy(crc16, 0, writeBytes, orderBytes.size, 2)
        return writeBytes
    }

    /**
     * 组成读单个线圈命令
     * @param orderBytes
     * @param addr
     * @param state
     * @return
     */
    private fun makeOrderBytes(orderBytes: ByteArray, addr: Byte, state: Byte): ByteArray? {
        orderBytes[3] = addr
        orderBytes[4] = state
        val i = CRC16Util.calcCrc16(orderBytes)
        val crc16 = CRC16Util.getCrc(i)
        val writeBytes = ByteArray(orderBytes.size + 2)
        System.arraycopy(orderBytes, 0, writeBytes, 0, orderBytes.size)
        System.arraycopy(crc16, 0, writeBytes, orderBytes.size, 2)
        return writeBytes
    }


    /**
     * 字节数组转换为字符串
     *
     * @param buffer
     * @param length
     * 长度
     * @param start
     * 起始下标
     * @return
     */
    fun byteArrayToString(buffer: ByteArray, length: Int, start: Int): String? {
        val bs = ByteArray(length)
        for (a in 0 until length) {
            bs[a] = buffer[a + start]
        }
        var str: String? = null
        try {
            str = byteArrToHexStr(bs)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return str
    }

    @Throws(Exception::class)
    private fun byteArrToHexStr(arrB: ByteArray): String? {
        val iLen = arrB.size
        // 每个byte用两个字符才能表示，所以字符串的长度是数组长度的两倍
        val sb = StringBuffer(iLen * 2)
        for (i in 0 until iLen) {
            var intTmp = arrB[i].toInt()
            // 把负数转换为正数
            while (intTmp < 0) intTmp += 256
            // 小于0F的数需要在前面补0
            if (intTmp < 16) {
                sb.append("0")
            }
            sb.append(intTmp.toString(16))
        }
        return sb.toString()
    }

    /**
     * byte转bit字符串
     * @param b
     * @return
     */
    fun byteToBit(b: Byte): String {
        return (""
                + (b.toInt() shr 7 and 0x1).toByte() + (b.toInt() shr 6 and 0x1).toByte() + (b.toInt() shr 5 and 0x1).toByte() + (b.toInt() shr 4 and 0x1).toByte() + (b.toInt() shr 3 and 0x1).toByte() + (b.toInt() shr 2 and 0x1).toByte() + (b.toInt() shr 1 and 0x1).toByte() + (b.toInt() shr 0 and 0x1).toByte())
    }

    /**
     * 两个字节转换为int
     * @param highByte
     * @param lowByte
     * @return
     */
    private fun byteArrToInt(highByte: Byte, lowByte: Byte): Int {
        var high = highByte.toInt()
        if (high < 0) {
            high = high + 256
        }
        var low = lowByte.toInt()
        if (low < 0) {
            low = low + 256
        }
        return high * 256 + low
    }

    fun intTo2Bytes(value: Int): ByteArray {
        val bytes = ByteArray(2)
        bytes[0] = (value and 0xFF).toByte() // 取低8位
        bytes[1] = (value shr 8 and 0xFF).toByte() // 取次低8位
        return bytes
    }
    /**
     * 反转二进制转换
     */
    private fun convertToBinaryStringAndReverse(value: Int): String {
        return Integer.toBinaryString(value).padStart(16, '0').reversed()
    }


    fun hexToString(orderBytes: ByteArray): String {
        val b = StringBuffer()
        for (by in orderBytes) {
            b.append(by)
            b.append(",")
        }

        return b.toString()
    }

    fun hexToString(hex: String): String {
        val bytes = hex.chunked(2).map { it.toInt(16).toByte() }
        return String(bytes.toByteArray(), Charset.forName("GB2312"))
    }

    fun setLastLedAddress(address: Byte) {
        lastAddress = address
    }
}