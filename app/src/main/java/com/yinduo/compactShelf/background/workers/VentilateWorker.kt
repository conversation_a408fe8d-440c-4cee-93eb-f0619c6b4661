package com.yinduo.compactShelf.background.workers

import android.content.Context
import android.util.Log
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.yinduo.compactShelf.manager.SerialPortManager
import com.yinduo.compactShelf.room.entity.VentilateConfig
import com.yinduo.compactShelf.room.repository.VentilateConfigRepository
import com.yinduo.compactShelf.view.main.ScreensaverFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * 定时通风
 */
private const val TAG = "VentilateWorker"

class VentilateWorker(ctx: Context, params: WorkerParameters) : Worker(ctx, params) {
    override fun doWork(): Result {
        val config = VentilateConfigRepository.getConfig()
        Log.d("test","===========开启通风换气任务===使能==${config?.isEnable}")
        if (config != null && config.isEnable == 1) {
            if (ScreensaverFragment.isShowing){
                GlobalScope.async {
                    Log.d("test","===========屏保中开启上电========")
                    SerialPortManager.leftPowerOn()
                    SerialPortManager.rightPowerOn()
                    delay(5000)
                    startVentilate(config)
                }
            }else{
                startVentilate(config)
            }


        }
        return Result.success()
    }

    private fun startVentilate(config: VentilateConfig){
        SerialPortManager.ventilate()
        Log.d("test","===========开启闭架任务  延迟时间==${config?.startUpTime}分钟===============")
        val workRequest =
            OneTimeWorkRequestBuilder<CloseShelf>()
                .setInitialDelay(config.startUpTime.toLong(), TimeUnit.MINUTES)
                .build()
        WorkManager.getInstance().enqueue(workRequest)
    }
}

internal class CloseShelf(ctx: Context, params: WorkerParameters) : Worker(ctx, params) {
    override fun doWork(): Result {
        Log.d("test","===========启动闭架操作=========")
        if (ScreensaverFragment.isShowing){
            GlobalScope.async {
                SerialPortManager.operateShelf(0x64, 0x03)
                delay(5000)
                SerialPortManager.leftPowerOff()
                SerialPortManager.rightPowerOff()
            }
        }else{
            SerialPortManager.operateShelf(0x64, 0x03)
        }

        return Result.success()
    }

}