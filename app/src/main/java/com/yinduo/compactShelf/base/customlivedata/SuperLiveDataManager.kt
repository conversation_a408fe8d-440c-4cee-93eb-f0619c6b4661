package com.yinduo.compactShelf.base.customlivedata

import androidx.fragment.app.DialogFragment
import com.yinduo.compactShelf.base.customlivedata.base.OneOffLiveData
import com.yinduo.compactShelf.view.main.bean.OrderBean


object SuperLiveDataManager {

    /**
     * 手动锁定状态
     */
    val getLockStatus = OneOffLiveData<Int>()

    /**
     * 串口线程状态
     */
    val serialStart = OneOffLiveData<Boolean>()

    /**
     * 本区除尘模式
     */
    val areaCleanType = OneOffLiveData<Int>()

    /**
     * 本列除尘模式
     */
    val shelfCleanType = OneOffLiveData<Int>()

    /**
     * 本区风速
     */
    val areaWind = OneOffLiveData<Int>()

    /**
     * 本列风速
     */
    val shelfWind = OneOffLiveData<Int>()

    /**
     * 本区除尘使能
     */
    val areaHaveClean = OneOffLiveData<Int>()

    /**
     * 本列除尘使能
     */
    val shelfHaveClean = OneOffLiveData<Int>()

    /**
     * 本列除尘使能
     */
    val serialData = OneOffLiveData<OrderBean>()

    /**
     * 打开Dialog
     */
    val dialogName = OneOffLiveData<DialogFragment>()

    /**
     * 组件通讯
     */
    val componentMessage = OneOffLiveData<String>()

    /**
     * 打开队列
     */
    val openQueueOrder = OneOffLiveData<Boolean>()

    /**
     * 架体移动状态
     */
    val shelfMotionState = OneOffLiveData<Int>()
    val motionState = OneOffLiveData<Int>()

    /**
     * 滤网剩余用时
     */
    val filterSurplusTime = OneOffLiveData<Int>()

    /**
     * 滤网是否报警
     */
    val filterIsAlarm = OneOffLiveData<Int>()

    /**
     * 开架位置
     */
    val openPosition = OneOffLiveData<String>()

    /**
     * 通风状态
     */
    val shelfVentilateState = OneOffLiveData<Int>()
    /**
     * 手动定位灯状态
     */
    val locateLightState = OneOffLiveData<Int>()
    /**
     * 移动列上传内容
     */
    val molSearchText = OneOffLiveData<String>()
    /**
     * 休眠控制
     * true  休眠  false 唤醒
     */
    val sleepControl = OneOffLiveData<Boolean>()
    /**
     * 移动列搜索后续操作 1 下一条  2 上一条  3 开架
     */
    val shelfSearchOprate = OneOffLiveData<Int>()
    /**
     * 批量下写Led完成
     */
    val writeLedComplete = OneOffLiveData<String>()
}