package com.yinduo.compactShelf.utils

import android.Manifest
import android.util.Log
import android.view.Surface
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.italkbb.prime.utils.IRequestPermission
import java.io.File

object CameraUtils {

    fun startPreview(activity: FragmentActivity, previewView :PreviewView,screenType:Int){
        PermissionUtil.reqPermission(object : IRequestPermission() {
            override fun accept() {
                Log.d("test", "===have permission===")
                val cameraProviderFuture = ProcessCameraProvider.getInstance(activity)
                cameraProviderFuture.addListener(Runnable {
                    val cameraProvider = cameraProviderFuture.get()
                    cameraProvider.unbindAll()
                    val preview: Preview = Preview.Builder()
                        .build()
                    var cameraSelector: CameraSelector = CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()
                    when (screenType) {
                        1 -> {
                            val cameraList = cameraProvider.availableCameraInfos
                            var previewDegreeVal: Int = 0
                            if (cameraList != null && cameraList.size > 1) {
                                Log.d(
                                    "test",
                                    "open M16G30 second camera; the sensor rotation is ${cameraList[1].sensorRotationDegrees}"
                                )
                                previewDegreeVal = cameraList[1].sensorRotationDegrees
                                cameraSelector = cameraList[1].cameraSelector
                            }
                            when (previewDegreeVal) {
                                270 -> {
                                    previewView.implementationMode =
                                        PreviewView.ImplementationMode.COMPATIBLE
                                    previewView.rotation = 270f
                                }
                            }
                        }
                    }
                    val hasCamera = cameraProvider.hasCamera(cameraSelector)
                    if(!hasCamera){
                        ToastUtils.showShort("相机不存在、请检查系统配置")
                        return@Runnable
                    }
                    preview.setSurfaceProvider(previewView.surfaceProvider)
                    cameraProvider.bindToLifecycle(
                        activity,
                        cameraSelector,
                        preview
                    )
                }, ContextCompat.getMainExecutor(activity))
            }

            override fun refuse() {
                super.refuse()
                Log.d("test", "===no permission===")
            }

            override fun noRemind() {
                Log.d("test", "===noremind===")
                ToastUtils.showShort("没有相机权限，请前往设置打开")
            }

        }, Manifest.permission.CAMERA)
    }

    fun takePic(activity: FragmentActivity, previewView :PreviewView,screenType:Int,userId:Long,
                doWork:(path:String)->Unit){
        PermissionUtil.reqPermission(object : IRequestPermission() {
            override fun accept() {
                Log.d("test", "===have permission===")
                val cameraProviderFuture = ProcessCameraProvider.getInstance(activity)
                cameraProviderFuture.addListener(Runnable {
                    val cameraProvider = cameraProviderFuture.get()
                    cameraProvider.unbindAll()
                    val preview: Preview = Preview.Builder()
                        .build()
                    var cameraSelector: CameraSelector = CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()
                    var imageCapture = ImageCapture.Builder()
                        .setTargetRotation(previewView.display.rotation)
                        .build()
                    when (screenType) {
                        1 -> {
                            val cameraList = cameraProvider.availableCameraInfos
                            var previewDegreeVal: Int = 0
                            if (cameraList != null && cameraList.size > 1) {
                                Log.d(
                                    "test",
                                    "open M16G30 second camera; the sensor rotation is ${cameraList[1].sensorRotationDegrees}"
                                )
                                previewDegreeVal = cameraList[1].sensorRotationDegrees
                                cameraSelector = cameraList[1].cameraSelector
                            }
                            when (previewDegreeVal) {
                                270 -> {
                                    previewView.implementationMode =
                                        PreviewView.ImplementationMode.COMPATIBLE
                                    previewView.rotation = 270f
                                    imageCapture = ImageCapture.Builder()
                                        .setTargetRotation(Surface.ROTATION_90)
                                        .build()
                                }
                            }
                        }
                    }
                    val hasCamera = cameraProvider.hasCamera(cameraSelector)
                    if(!hasCamera){
                        ToastUtils.showShort("相机不存在、请检查系统配置")
                        return@Runnable
                    }
                    preview.setSurfaceProvider(previewView.surfaceProvider)
                    cameraProvider.bindToLifecycle(
                        activity,
                        cameraSelector,
                        imageCapture,
                        preview
                    )

                    val dic = File(activity.getExternalFilesDir(
                        "user_login")?.absolutePath?:(activity.externalCacheDir?.absolutePath
                            +"/user_login"))
                    if (!dic.exists()) {
                        dic.mkdirs()
                    }
                    val outputFileOptions = ImageCapture.OutputFileOptions.Builder(
                        File(dic.absolutePath + "/$userId-" + System.currentTimeMillis() + ".jpg")
                    ).build()
                    imageCapture.takePicture(outputFileOptions, ContextCompat.getMainExecutor(activity),
                        object : ImageCapture.OnImageSavedCallback {
                            override fun onError(error: ImageCaptureException) {
                                Log.d("test", "===error===${error.printStackTrace()}")
                                ToastUtils.showShort("拍照失败")
                            }

                            override fun onImageSaved(outputFileResults: ImageCapture.OutputFileResults) {
                                val imgPath =
                                    SDCardUtils.getRealFilePath(activity, outputFileResults.savedUri)
                                        ?: ""
                                Log.d("test", "===照片路径===$imgPath")
                                doWork(imgPath)
                            }
                        })
                }, ContextCompat.getMainExecutor(activity))
            }

            override fun refuse() {
                super.refuse()
                Log.d("test", "===no permission===")
            }

            override fun noRemind() {
                Log.d("test", "===noremind===")
                ToastUtils.showShort("没有相机权限，请前往设置打开")
            }

        }, Manifest.permission.CAMERA)
    }
    fun stopPreview(activity: FragmentActivity){
        Log.d("test", "===stopPreview===")
        val cameraProviderFuture = ProcessCameraProvider.getInstance(activity)
        cameraProviderFuture.addListener(Runnable {
            val cameraProvider = cameraProviderFuture.get()
            cameraProvider.unbindAll()
        },ContextCompat.getMainExecutor(activity))
    }
}