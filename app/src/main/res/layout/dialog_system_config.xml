<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="click"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="418dp"
        android:layout_height="340dp"
        android:paddingStart="15dp"
        android:paddingTop="10dp">
        <TextView
            android:id="@+id/tv_serial_port"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/serial_port"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <EditText
            android:id="@+id/et_serial_port"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:layout_marginStart="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            app:layout_constraintTop_toTopOf="@id/tv_serial_port"
            app:layout_constraintBottom_toBottomOf="@id/tv_serial_port"
            app:layout_constraintStart_toEndOf="@id/tv_serial_port"/>

        <TextView
            android:id="@+id/tv_camera_rotate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:text="@string/camera_rotate"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/et_serial_port"
            app:layout_constraintTop_toTopOf="@id/tv_serial_port" />

        <EditText
            android:id="@+id/et_camera_rotate"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:layout_marginStart="15dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:paddingStart="8dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tv_camera_rotate"
            app:layout_constraintStart_toEndOf="@id/tv_camera_rotate"
            app:layout_constraintTop_toTopOf="@id/tv_camera_rotate" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:text="@string/degree"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/et_camera_rotate"
            app:layout_constraintTop_toTopOf="@id/tv_camera_rotate" />
        <TextView
            android:id="@+id/tv_server_ip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_ip"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="@id/tv_serial_port"
            app:layout_constraintTop_toBottomOf="@id/tv_serial_port"/>
        <EditText
            android:id="@+id/et_server_ip"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_server_ip"
            app:layout_constraintBottom_toBottomOf="@id/tv_server_ip"
            app:layout_constraintStart_toStartOf="@id/et_serial_port"/>
        <TextView
            android:id="@+id/tv_server_port"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_port"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginStart="25dp"
            app:layout_constraintStart_toEndOf="@id/et_server_ip"
            app:layout_constraintTop_toTopOf="@id/tv_server_ip"/>
        <EditText
            android:id="@+id/et_server_port"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_server_port"
            app:layout_constraintBottom_toBottomOf="@id/tv_server_port"
            app:layout_constraintStart_toStartOf="@id/et_camera_rotate"/>
        <TextView
            android:id="@+id/tv_self_ip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/self_ip"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="@id/tv_server_ip"
            app:layout_constraintTop_toBottomOf="@id/tv_server_ip"/>
        <EditText
            android:id="@+id/et_self_ip"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_self_ip"
            app:layout_constraintBottom_toBottomOf="@id/tv_self_ip"
            app:layout_constraintStart_toStartOf="@id/et_server_ip"/>
        <Button
            android:id="@+id/bt_get_ip"
            android:layout_width="30dp"
            android:layout_height="15dp"
            android:onClick="@{click}"
            android:text="@string/get_config"
            android:textColor="@color/white"
            android:textSize="7sp"
            android:layout_marginStart="89dp"
            android:background="@mipmap/icon_bt_dark_blue_r3"
            app:layout_constraintEnd_toEndOf="@id/et_self_ip"
            app:layout_constraintTop_toTopOf="@id/et_self_ip"/>

        <TextView
            android:id="@+id/tv_active_screensaver"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:text="@string/active_screensaver"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/et_self_ip"
            app:layout_constraintTop_toTopOf="@id/tv_self_ip" />

        <Spinner
            android:id="@+id/et_active_screensaver"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tv_active_screensaver"
            app:layout_constraintStart_toStartOf="@id/et_server_port"
            app:layout_constraintTop_toTopOf="@id/tv_active_screensaver"
            tools:listitem="@layout/item_enable" />

        <TextView
            android:id="@+id/tv_standby_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/standby_time"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="@id/tv_self_ip"
            app:layout_constraintTop_toBottomOf="@id/tv_self_ip" />

        <EditText
            android:id="@+id/et_standby_time"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:paddingStart="8dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tv_standby_time"
            app:layout_constraintStart_toStartOf="@id/et_self_ip"
            app:layout_constraintTop_toTopOf="@id/tv_standby_time" />

        <TextView
            android:id="@+id/tv_smoke_alarm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:text="@string/smoke_alarm"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/et_standby_time"
            app:layout_constraintTop_toTopOf="@id/tv_standby_time" />

        <Spinner
            android:id="@+id/et_smoke_alarm"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tv_smoke_alarm"
            app:layout_constraintStart_toStartOf="@id/et_active_screensaver"
            app:layout_constraintTop_toTopOf="@id/tv_smoke_alarm"
            tools:listitem="@layout/item_enable" />
        <TextView
            android:id="@+id/tv_sensor_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="传感器类型"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginStart="25dp"
            android:layout_marginTop="@dimen/dp_10"
            app:layout_constraintStart_toEndOf="@id/et_standby_time"
            app:layout_constraintTop_toBottomOf="@id/tv_smoke_alarm"/>
        <Spinner
            android:id="@+id/et_sensor_type"
            android:layout_width="130dp"
            android:layout_height="15dp"
            tools:listitem="@layout/item_enable"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_sensor_type"
            app:layout_constraintBottom_toBottomOf="@id/tv_sensor_type"
            app:layout_constraintStart_toStartOf="@id/et_active_screensaver"/>
        <TextView
            android:id="@+id/tv_standby_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="@string/standby_close_shelf"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="13dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            app:layout_constraintTop_toBottomOf="@id/tv_standby_time"
            app:layout_constraintStart_toStartOf="@id/tv_standby_time"/>
        <TextView
            android:id="@+id/tv_standby_shutdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="@string/standby_close_power"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:layout_marginStart="22dp"
            app:layout_constraintTop_toTopOf="@id/tv_standby_close"
            app:layout_constraintStart_toEndOf="@id/tv_standby_close"/>

        <TextView
            android:id="@+id/tv_stop_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="22dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:onClick="@{click}"
            android:text="@string/stop_enable"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/tv_standby_shutdown"
            app:layout_constraintTop_toTopOf="@id/tv_standby_shutdown" />

        <TextView
            android:id="@+id/tv_area_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/my_area_order"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="13dp"
            app:layout_constraintStart_toStartOf="@id/tv_standby_time"
            app:layout_constraintTop_toBottomOf="@id/tv_standby_close"/>
        <EditText
            android:id="@+id/et_area_order"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_area_order"
            app:layout_constraintBottom_toBottomOf="@id/tv_area_order"
            app:layout_constraintStart_toStartOf="@id/et_standby_time"/>
        <TextView
            android:id="@+id/tv_area_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/my_area_number"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginStart="25dp"
            app:layout_constraintStart_toEndOf="@id/et_area_order"
            app:layout_constraintTop_toTopOf="@id/tv_area_order"/>
        <EditText
            android:id="@+id/et_area_number"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_area_number"
            app:layout_constraintBottom_toBottomOf="@id/tv_area_number"
            app:layout_constraintStart_toStartOf="@id/et_smoke_alarm"/>

        <TextView
            android:id="@+id/tv_column_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/column_order"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="@id/tv_area_order"
            app:layout_constraintTop_toBottomOf="@id/tv_area_order"/>
        <EditText
            android:id="@+id/et_column_order"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_column_order"
            app:layout_constraintBottom_toBottomOf="@id/tv_column_order"
            app:layout_constraintStart_toStartOf="@id/et_standby_time"/>

        <TextView
            android:id="@+id/tv_help_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:text="@string/help_column_order"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/et_column_order"
            app:layout_constraintTop_toTopOf="@id/tv_column_order" />
        <EditText
            android:visibility="invisible"
            android:id="@+id/et_help_order"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_help_order"
            app:layout_constraintBottom_toBottomOf="@id/tv_help_order"
            app:layout_constraintStart_toStartOf="@id/et_smoke_alarm"/>

        <TextView
            android:id="@+id/tv_section_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/section_number"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="@id/tv_column_order"
            app:layout_constraintTop_toBottomOf="@id/tv_column_order"/>
        <EditText
            android:id="@+id/et_section_number"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_section_number"
            app:layout_constraintBottom_toBottomOf="@id/tv_section_number"
            app:layout_constraintStart_toStartOf="@id/et_standby_time"/>
        <TextView
            android:id="@+id/tv_layer_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/layer_number"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginStart="25dp"
            app:layout_constraintStart_toEndOf="@id/et_section_number"
            app:layout_constraintTop_toTopOf="@id/tv_section_number"/>
        <EditText
            android:id="@+id/et_layer_number"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_layer_number"
            app:layout_constraintBottom_toBottomOf="@id/tv_layer_number"
            app:layout_constraintStart_toStartOf="@id/et_smoke_alarm"/>

        <TextView
            android:id="@+id/tv_led_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="左侧列数"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            app:layout_constraintStart_toStartOf="@id/tv_section_number"
            app:layout_constraintTop_toBottomOf="@id/tv_section_number" />
        <EditText
            android:id="@+id/et_led_number"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:inputType="number"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_led_number"
            app:layout_constraintBottom_toBottomOf="@id/tv_led_number"
            app:layout_constraintStart_toStartOf="@id/et_standby_time"/>

        <TextView
            android:id="@+id/tv_virtual_width"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:text="右侧列数"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:inputType="number"
            app:layout_constraintStart_toEndOf="@id/et_led_number"
            app:layout_constraintTop_toTopOf="@id/tv_led_number" />
        <EditText
            android:id="@+id/et_virtual_width"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:paddingStart="8dp"
            android:textSize="7sp"
            android:textColor="@color/white"
            android:singleLine="true"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_virtual_width"
            app:layout_constraintBottom_toBottomOf="@id/tv_virtual_width"
            app:layout_constraintStart_toStartOf="@id/et_smoke_alarm"/>

        <TextView
            android:id="@+id/tv_temp_reparation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/temp_reparation"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="@id/tv_led_number"
            app:layout_constraintTop_toBottomOf="@id/tv_led_number" />

        <EditText
            android:id="@+id/et_temp_reparation"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:paddingStart="8dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tv_temp_reparation"
            app:layout_constraintStart_toStartOf="@id/et_standby_time"
            app:layout_constraintTop_toTopOf="@id/tv_temp_reparation" />

        <TextView
            android:id="@+id/tv_rh_reparation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:text="@string/rh_reparation"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/et_temp_reparation"
            app:layout_constraintTop_toTopOf="@id/tv_temp_reparation" />

        <EditText
            android:id="@+id/et_rh_reparation"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:paddingStart="8dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tv_rh_reparation"
            app:layout_constraintStart_toStartOf="@id/et_smoke_alarm"
            app:layout_constraintTop_toTopOf="@id/tv_rh_reparation" />

        <TextView
            android:id="@+id/tv_connect_repository"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/connect_repository"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="@id/tv_temp_reparation"
            app:layout_constraintTop_toBottomOf="@id/tv_temp_reparation" />

        <EditText
            android:id="@+id/et_connect_repository"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:paddingStart="8dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tv_connect_repository"
            app:layout_constraintStart_toStartOf="@id/et_standby_time"
            app:layout_constraintTop_toTopOf="@id/tv_connect_repository" />

        <TextView
            android:id="@+id/tv_refresh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="36dp"
            android:background="@drawable/stroke_00c6ff_bottom_1"
            android:drawableStart="@mipmap/icon_refresh"
            android:drawablePadding="4dp"
            android:onClick="@{click}"
            android:paddingBottom="4dp"
            android:text="@string/refresh"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/et_connect_repository"
            app:layout_constraintTop_toTopOf="@id/tv_connect_repository" />
        <TextView
            android:id="@+id/tv_power_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/power_setting"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:background="@drawable/stroke_00c6ff_bottom_1"
            android:drawableStart="@mipmap/icon_open_shutdown"
            android:onClick="@{click}"
            android:drawablePadding="4dp"
            android:paddingBottom="4dp"
            android:layout_marginStart="20dp"
            app:layout_constraintTop_toTopOf="@id/tv_refresh"
            app:layout_constraintStart_toEndOf="@id/tv_refresh"/>

        <TextView
            android:id="@+id/tv_author_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:background="@drawable/stroke_00c6ff_bottom_1"
            android:drawableStart="@mipmap/icon_author"
            android:drawablePadding="4dp"
            android:onClick="@{click}"
            android:paddingBottom="4dp"
            android:text="@string/author_setting"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/tv_power_setting"
            app:layout_constraintTop_toTopOf="@id/tv_power_setting" />

        <TextView
            android:id="@+id/tv_auto_open_shelf"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:onClick="@{click}"
            android:text="@string/auto_open_shelf"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="@id/tv_connect_repository"
            app:layout_constraintTop_toBottomOf="@id/tv_connect_repository" />
        <TextView
            android:id="@+id/tv_scan_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:onClick="@{click}"
            android:text="开启扫码查询"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            app:layout_constraintStart_toStartOf="@id/tv_connect_repository"
            app:layout_constraintTop_toBottomOf="@id/tv_connect_repository" />
        <TextView
            android:id="@+id/tv_lamb_reverse"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="42dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:onClick="@{click}"
            android:text="@string/position_lamb_reverse"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/tv_auto_open_shelf"
            app:layout_constraintTop_toTopOf="@id/tv_auto_open_shelf" />
        <TextView
            android:id="@+id/tv_face_exit_saver"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="42dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:onClick="@{click}"
            android:text="人脸退出屏保"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            app:layout_constraintStart_toEndOf="@id/tv_auto_open_shelf"
            app:layout_constraintTop_toTopOf="@id/tv_auto_open_shelf" />
        <TextView
            android:id="@+id/tv_a_reverse"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:onClick="@{click}"
            android:text="@string/a_reverse"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="@id/tv_auto_open_shelf"
            app:layout_constraintTop_toBottomOf="@id/tv_auto_open_shelf" />

        <TextView
            android:id="@+id/tv_b_reverse"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:onClick="@{click}"
            android:text="@string/b_reverse"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="@id/tv_lamb_reverse"
            app:layout_constraintTop_toTopOf="@id/tv_a_reverse" />

        <TextView
            android:id="@+id/tv_voice_female"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="@string/voice_female"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="10dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            app:layout_constraintTop_toBottomOf="@id/tv_a_reverse"
            app:layout_constraintStart_toStartOf="@id/tv_a_reverse"/>
        <TextView
            android:id="@+id/tv_voice_male"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="@string/voice_male"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            app:layout_constraintTop_toTopOf="@id/tv_voice_female"
            app:layout_constraintStart_toStartOf="@id/tv_lamb_reverse"/>

        <TextView
            android:id="@+id/tv_delay_close_lamb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="47dp"
            android:text="@string/delay_close_lamb"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/tv_lamb_reverse"
            app:layout_constraintTop_toTopOf="@id/tv_auto_open_shelf" />

        <EditText
            android:id="@+id/et_delay_close_lamb"
            android:layout_width="130dp"
            android:layout_height="15dp"
            android:layout_marginStart="14dp"
            android:background="@mipmap/icon_et_confit_bg"
            android:ellipsize="end"
            android:imeOptions="actionDone"
            android:paddingStart="8dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tv_delay_close_lamb"
            app:layout_constraintStart_toEndOf="@id/tv_delay_close_lamb"
            app:layout_constraintTop_toTopOf="@id/tv_delay_close_lamb" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:text="@string/second"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/et_delay_close_lamb"
            app:layout_constraintTop_toTopOf="@id/tv_delay_close_lamb" />
        <TextView
            android:id="@+id/tv_system_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="系统名称"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="@dimen/dp_10"
            app:layout_constraintStart_toStartOf="@id/tv_delay_close_lamb"
            app:layout_constraintTop_toBottomOf="@id/tv_delay_close_lamb"/>
        <Spinner
            android:id="@+id/et_system_name"
            android:layout_width="130dp"
            android:layout_height="15dp"
            tools:listitem="@layout/item_enable"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_system_name"
            app:layout_constraintBottom_toBottomOf="@id/tv_system_name"
            app:layout_constraintStart_toStartOf="@id/et_delay_close_lamb"/>

        <TextView
            android:id="@+id/tv_clean_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="除尘名称"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:layout_marginTop="@dimen/dp_10"
            app:layout_constraintStart_toStartOf="@id/tv_delay_close_lamb"
            app:layout_constraintTop_toBottomOf="@id/tv_system_name"/>
        <Spinner
            android:id="@+id/et_clean_name"
            android:layout_width="130dp"
            android:layout_height="15dp"
            tools:listitem="@layout/item_enable"
            android:background="@mipmap/icon_et_confit_bg"
            app:layout_constraintTop_toTopOf="@id/tv_clean_name"
            app:layout_constraintBottom_toBottomOf="@id/tv_clean_name"
            app:layout_constraintStart_toStartOf="@id/et_delay_close_lamb"/>
        <TextView
            android:id="@+id/tv_listen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/voice_listen"
            android:textSize="7sp"
            android:textColor="@color/color_00c6ff"
            android:background="@drawable/stroke_00c6ff_bottom_1"
            android:drawableStart="@mipmap/icon_listening"
            android:onClick="@{click}"
            android:drawablePadding="4dp"
            android:paddingBottom="4dp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginStart="231dp"
            app:layout_constraintTop_toBottomOf="@id/et_clean_name"
            app:layout_constraintStart_toEndOf="@id/tv_voice_male" />
        
        <Button
            android:id="@+id/bt_get_config"
            android:layout_width="46dp"
            android:layout_height="19dp"
            android:onClick="@{click}"
            android:text="@string/get_config"
            android:textColor="@color/white"
            android:textSize="8sp"
            android:layout_marginTop="18dp"
            android:layout_marginStart="89dp"
            android:background="@drawable/selector_dialog_config_button"
            app:layout_constraintStart_toStartOf="@id/tv_voice_female"
            app:layout_constraintTop_toBottomOf="@id/tv_voice_female"/>
        <Button
            android:id="@+id/bt_send_config"
            android:layout_width="46dp"
            android:layout_height="19dp"
            android:onClick="@{click}"
            android:text="@string/send_config"
            android:textColor="@color/white"
            android:textSize="8sp"
            android:layout_marginStart="7dp"
            android:background="@drawable/selector_dialog_config_button"
            app:layout_constraintStart_toEndOf="@id/bt_get_config"
            app:layout_constraintTop_toTopOf="@id/bt_get_config"/>
        <Button
            android:id="@+id/bt_save"
            android:layout_width="46dp"
            android:layout_height="19dp"
            android:onClick="@{click}"
            android:text="@string/save"
            android:textColor="@color/white"
            android:textSize="8sp"
            android:layout_marginStart="7dp"
            android:background="@drawable/selector_dialog_config_button"
            app:layout_constraintStart_toEndOf="@id/bt_send_config"
            app:layout_constraintTop_toTopOf="@id/bt_send_config"/>

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_00c6ff"
            android:textSize="7sp"
            android:text="版本号"
            android:layout_marginBottom="15dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>