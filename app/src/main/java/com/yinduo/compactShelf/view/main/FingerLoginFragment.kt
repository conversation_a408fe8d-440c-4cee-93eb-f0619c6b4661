package com.yinduo.compactShelf.view.main

import android.os.SystemClock
import android.util.Log
import android.view.View
import android.view.animation.LinearInterpolator
import android.view.animation.RotateAnimation
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.base.BaseFragment
import com.yinduo.compactShelf.databinding.FragmentLoginFingerBinding
import com.yinduo.compactShelf.log.UserLog
import com.yinduo.compactShelf.manager.SerialPortManager
import com.yinduo.compactShelf.manager.finger.DevComm
import com.yinduo.compactShelf.manager.finger.IUsbConnState
import com.yinduo.compactShelf.room.repository.UserRepository
import com.yinduo.compactShelf.utils.CameraUtils
import com.yinduo.compactShelf.utils.SpUtils
import com.yinduo.compactShelf.utils.ToastUtils
import com.yinduo.compactShelf.view.main.bean.LoginBean
import com.yinduo.compactShelf.view.main.viewModel.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FingerLoginFragment: BaseFragment<FragmentLoginFingerBinding, MainViewModel>(), View.OnClickListener  {
    private var screenType: Int = 0
    private var isTakePic: Boolean = false
    private var m_nPassedTime: Long = 0L
    private var userName = ""
    private var fingerId = -1
    private lateinit var fingerDevComm: DevComm
    private var isCancel: Boolean = true //是否手动关闭
    private lateinit var m_binImage: ByteArray
    override fun getLayoutId(): Int {
       return R.layout.fragment_login_finger
    }

    override fun getViewModel(): Class<MainViewModel> {
        return MainViewModel::class.java
    }
    override fun initView() {
        binding.click =  this
        m_binImage = ByteArray(1024 * 100)
    }

    override fun initData() {
        fingerDevComm = DevComm(requireActivity(),fingerConnectionCallback)
        isTakePic = SpUtils.CACHE.getBoolean("login_pic", false)
        binding.clFace.visibility = if (isTakePic) View.VISIBLE else View.GONE
    }
    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        Log.d("test","===setUserVisibleHint===$isVisibleToUser===finger")
    }

    override fun onResume() {
        super.onResume()
        Log.d("test","===onresume===finger==")
        val rotateAnimation = RotateAnimation(
            0f, 360f,
            RotateAnimation.RELATIVE_TO_SELF, 0.5f,
            RotateAnimation.RELATIVE_TO_SELF, 0.5f,
        )
        rotateAnimation.duration = 2000
        rotateAnimation.fillAfter = true
        rotateAnimation.interpolator = LinearInterpolator()
        rotateAnimation.repeatCount = -1
        binding.ivVerify.startAnimation(rotateAnimation)
//        lifecycleScope.launch(Dispatchers.IO){
//            val users = UserRepository.getUserByName(userName)
//            withContext(Dispatchers.Main){
//                if (users.isNotEmpty()){
//                    val user = users[0]
//                    if (user.finger.isNullOrEmpty()){
//                        ToastUtils.showShort("未找到该用户的指纹信息，请重新录入")
//                        Log.d("test","===未找到该用户的指纹信息，请重新录入===")
//                        return@withContext
//                    }
//                    fingerId = user.finger.toInt()
//                    Log.d("test","===找到该用户的指纹信息，id===$fingerId")
//                    openFingerDevices()
//                }else{
//                    ToastUtils.showShort("未找到该用户，请重新输入")
//                    Log.d("test","===未找到该用户，请重新输入===")
//                }
//            }
//
//        }
        openFingerDevices()
        if (isTakePic && isShowing){
            viewModel.advancedConfigLiveData.observe(viewLifecycleOwner) {
                screenType = it?.screenType ?: 0
                CameraUtils.startPreview(requireActivity(),binding.preview,screenType)
            }
        }
    }
    private fun openFingerDevices() {
        val w_strInfo = arrayOfNulls<String>(1)
        if (!fingerDevComm.IsInit()) {
            if (!fingerDevComm.OpenComm("USB", 9600)) {
                Log.d("test","====初始化usb指纹识别失败=====")
                return
            }
        }
        if (fingerDevComm.Run_TestConnection() == DevComm.ERR_SUCCESS) {
            if (fingerDevComm.Run_GetDeviceInfo(w_strInfo) == DevComm.ERR_SUCCESS) {
                Log.d("test","====打开usb指纹识别成功=====")
                startIdentify()
            } else{
                Log.d("test","====打开usb指纹识别失败=====")
            }
        } else {
            Log.d("test","====打开usb指纹识别失败=====")
            fingerDevComm.CloseComm()
        }
    }

    override fun onPause() {
        super.onPause()
        fingerDevComm.CloseComm()
        Log.d("test", "onPause: finger dev close")
    }
    override fun onDestroyView() {
        super.onDestroyView()
        fingerDevComm.CloseComm()
    }
    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        Log.d("test","===onhidden===$hidden===finger")
        if (!hidden){
            val rotateAnimation = RotateAnimation(
                0f, 360f,
                RotateAnimation.RELATIVE_TO_SELF, 0.5f,
                RotateAnimation.RELATIVE_TO_SELF, 0.5f,
            )
            rotateAnimation.duration = 2000
            rotateAnimation.fillAfter = true
            rotateAnimation.interpolator = LinearInterpolator()
            rotateAnimation.repeatCount = -1
            binding.ivVerify.startAnimation(rotateAnimation)
            fingerDevComm = DevComm(requireActivity(),fingerConnectionCallback)
//            lifecycleScope.launch(Dispatchers.IO){
//                val users = UserRepository.getUserByName(userName)
//                withContext(Dispatchers.Main){
//                    if (users.isNotEmpty()){
//                        val user = users[0]
//                        if (user.finger.isNullOrEmpty()){
//                            ToastUtils.showShort("未找到该用户的指纹信息，请重新录入")
//                            Log.d("test","===未找到该用户的指纹信息，请重新录入===")
//                        }
//                        fingerId = user.finger.toInt()
//                        fingerDevComm = DevComm(requireActivity(),fingerConnectionCallback)
//                    }else{
//                        ToastUtils.showShort("未找到该用户，请重新输入")
//                        Log.d("test","===未找到该用户，请重新输入===")
//                    }
//                }
//
//            }
        }

    }

    /**
     * 开始识别
     */
    private fun startVerify() {
        var w_nRet: Int
        val w_nState = IntArray(1)

        if (!fingerDevComm.IsInit()){
            binding.tvVerify.text = "初始化失败"
            return
        }

        w_nRet = fingerDevComm.Run_GetStatus(fingerId, w_nState)

        if (w_nRet != DevComm.ERR_SUCCESS) {
            binding.tvVerify.text = GetErrorMsg(w_nRet)
            return
        }

        if (w_nState[0] == DevComm.GD_TEMPLATE_EMPTY) {
            binding.tvVerify.text = "未找到该用户指纹"
            return
        }

        binding.tvVerify.text = "放入手指"
        fingerDevComm.Run_SLEDControl(1)
        isCancel = false
        lifecycleScope.launch(Dispatchers.IO) {
            var w_nRet = 0
            var w_nLearned = IntArray(1)
            var w_nWidth = IntArray(1)
            var w_nHeight = IntArray(1)
            if (capturing() < 0) return@launch
            withContext(Dispatchers.Main){
                binding.tvVerify.text = "拿起手指"
            }

            // Up Cpatured Image
            if (fingerDevComm.m_nConnected == 2.toByte()) {
                w_nRet = fingerDevComm.Run_UpImage(0, m_binImage, w_nWidth, w_nHeight)
                if (w_nRet != DevComm.ERR_SUCCESS) {
                    val error = GetErrorMsg(w_nRet)
                    withContext(Dispatchers.Main){
                        binding.tvVerify.text = "识别失败"
                    }
                    ToastUtils.showShort("===识别失败=====$error")
                    return@launch
                }

            }

            // Create template
            m_nPassedTime = SystemClock.elapsedRealtime()
            w_nRet = fingerDevComm.Run_Generate(0)
            if (w_nRet != DevComm.ERR_SUCCESS) {
                val error = GetErrorMsg(w_nRet)
                withContext(Dispatchers.Main){
                    binding.tvVerify.text = "识别失败"
                }
                ToastUtils.showShort("===识别失败=====$error")
                return@launch
            }

            // Verify
            w_nRet = fingerDevComm.Run_Verify(fingerId, 0, w_nLearned)
            m_nPassedTime = SystemClock.elapsedRealtime() - m_nPassedTime
            val result = if (w_nRet == DevComm.ERR_SUCCESS){
               "认证成功"
            }else {
                val error = GetErrorMsg(w_nRet)
                ToastUtils.showShort("===识别失败=====$error")
                "认证失败"
            }
            withContext(Dispatchers.Main){
                binding.tvVerify.text = result
                if (result == "认证成功"){
                    if (isTakePic){
                        CameraUtils.takePic(requireActivity(),binding.preview,screenType,fingerId.toLong()){
                            ToastUtils.showShort("登录成功")
                            lifecycleScope.launch(Dispatchers.IO){
                                UserLog.logLogin(it)
                                //登录成功后给移动列上电
                                withContext(Dispatchers.Main){
                                    SerialPortManager.leftPowerOn()
                                    SerialPortManager.rightPowerOn()
                                    findNavController().navigate(R.id.action_loginFragment_to_mainFragment)
                                }
                            }
                        }
                    }else{
                        ToastUtils.showShort("登录成功")
                        withContext(Dispatchers.IO){
                            UserLog.logLogin()
                        }
                        //登录成功后给移动列上电
                        SerialPortManager.leftPowerOn()
                        SerialPortManager.rightPowerOn()
                        findNavController().navigate(R.id.action_loginFragment_to_mainFragment)
                    }
                }

            }

        }
    }

    /**
     * 从指纹库搜索指纹
     */
    fun startIdentify() {
        if (!fingerDevComm.IsInit()){
            binding.tvVerify.text = "初始化失败"
            return
        }
        fingerDevComm.Run_SLEDControl(1)
        binding.tvVerify.text = "放入手指"
        isCancel = false
        lifecycleScope.launch(Dispatchers.IO) {
            var w_nRet = 0
            var w_nID = IntArray(1)
            var w_nLearned = IntArray(1)
            var w_nWidth = IntArray(1)
            var w_nHeight = IntArray(1)
            var isContinue = true
            while (isContinue) {
                if (capturing() < 0) return@launch
                withContext(Dispatchers.Main){
                    binding.tvVerify.text = "拿起手指"
                }
                // Up Cpatured Image
                if (fingerDevComm.m_nConnected == 2.toByte()) {
                    w_nRet =
                        fingerDevComm.Run_UpImage(0, m_binImage, w_nWidth, w_nHeight)
                    if (w_nRet != DevComm.ERR_SUCCESS) {
                        val error = GetErrorMsg(w_nRet)
                        withContext(Dispatchers.Main){
                            binding.tvVerify.text = "识别失败"
                        }
                        ToastUtils.showShort("===识别失败=====$error")
                        return@launch
                    }
                }

                // Create template
                m_nPassedTime = SystemClock.elapsedRealtime()
                w_nRet = fingerDevComm.Run_Generate(0)
                if (w_nRet != DevComm.ERR_SUCCESS) {
                    val error = GetErrorMsg(w_nRet)
                    withContext(Dispatchers.Main){
                        binding.tvVerify.text = "识别失败"
                        ToastUtils.showShort("===识别失败=====$error")
                    }

                    if (w_nRet == DevComm.ERR_CONNECTION) {
                        fingerDevComm.Run_SLEDControl(0)
                        return@launch
                    } else {
                        delay(1000)
                        continue
                    }
                }

                // Identify
                w_nRet =
                    fingerDevComm.Run_Search(0, 1, 500, w_nID, w_nLearned)
                m_nPassedTime = SystemClock.elapsedRealtime() - m_nPassedTime
                val result = if (w_nRet == DevComm.ERR_SUCCESS){
                    "认证成功"
                }else {
                    val error = GetErrorMsg(w_nRet)
                    withContext(Dispatchers.Main){
                        ToastUtils.showShort("===识别失败=====$error")
                    }
                    "认证失败"
                }
                // m_strPost = String.format(
                //                        "Result : Success\r\nTemplate No : %d, Learn Result : %d\r\nMatch Time : %dms",
                //                        w_nID[0], w_nLearned[0], m_nPassedTime
                //                    )
                if (result == "认证成功"){
                    Log.d("test","===识别结果===templateNo=== ${w_nID[0]}")
                    val userList = UserRepository.getUserByFinger(w_nID[0].toString())
                    withContext(Dispatchers.Main){
                        binding.tvVerify.text = result
                        if (userList.isEmpty()){
                            ToastUtils.showShort("登录失败")
                        }else{
                            val loginBean = LoginBean()
                            val userInfo = userList[0]
                            loginBean.name = userInfo.loginName.ifEmpty { userInfo.userName }
                            SpUtils.LASTING.putObject("last_login",loginBean)
                            MainActivity.currentUser = userInfo
                            if (isTakePic){
                                CameraUtils.takePic(requireActivity(),binding.preview,screenType,userInfo.userId){
                                    ToastUtils.showShort("登录成功")
                                    lifecycleScope.launch(Dispatchers.IO){
                                        UserLog.logLogin(it)
                                        //登录成功后给移动列上电
                                        withContext(Dispatchers.Main){
                                            SerialPortManager.leftPowerOn()
                                            SerialPortManager.rightPowerOn()
                                            findNavController().navigate(R.id.action_loginFragment_to_mainFragment)
                                        }
                                    }
                                }
                            }else{
                                ToastUtils.showShort("登录成功")
                                withContext(Dispatchers.IO){
                                    UserLog.logLogin()
                                }
                                //登录成功后给移动列上电
                                SerialPortManager.leftPowerOn()
                                SerialPortManager.rightPowerOn()
                                findNavController().navigate(R.id.action_loginFragment_to_mainFragment)
                            }
                            isContinue = false
                        }
                    }
                }
                if (!isContinue){
                    return@launch
                }
            }



        }
    }
    fun setUserName(userName:String){
        this.userName = userName
    }
    override fun onClick(v: View) {
        when(v.id){

        }
    }

    private val fingerConnectionCallback  = object : IUsbConnState {
        override fun onUsbConnected() {
            val w_strInfo = arrayOfNulls<String>(1)

            if (fingerDevComm.Run_TestConnection() == DevComm.ERR_SUCCESS) {
                if (fingerDevComm.Run_GetDeviceInfo(w_strInfo) == DevComm.ERR_SUCCESS) {
                    Log.d("test","===打开指纹设备成功===")
                    ToastUtils.showShort("====打开指纹设备成功=====")
                    startIdentify()
                }else{
                    Log.d("test","===打开指纹设备失败===")
                    ToastUtils.showShort("====打开指纹设备失败=====")
                }
            } else {
                Log.d("test","===打开指纹设备失败===")
                ToastUtils.showShort("====打开指纹设备失败=====")
            }
        }

        override fun onUsbPermissionDenied() {
            Log.d("test","===没有USB权限===")
            ToastUtils.showShort("====没有USB权限=====")
        }

        override fun onDeviceNotFound() {
            Log.d("test","===没有找到指纹设备===")
            ToastUtils.showShort("====没有找到指纹设备=====")
        }

    }
    private fun GetErrorMsg(nErrorCode: Int): String {
        var str: String = ""
        str = when (nErrorCode) {
            DevComm.ERR_SUCCESS -> "Succcess"
            DevComm.ERR_VERIFY -> "Verify NG"
            DevComm.ERR_IDENTIFY -> "Identify NG"
            DevComm.ERR_EMPTY_ID_NOEXIST -> "Empty Template no Exist"
            DevComm.ERR_BROKEN_ID_NOEXIST -> "Broken Template no Exist"
            DevComm.ERR_TMPL_NOT_EMPTY -> "Template of this ID Already Exist"
            DevComm.ERR_TMPL_EMPTY -> "This Template is Already Empty"
            DevComm.ERR_INVALID_TMPL_NO -> "Invalid Template No"
            DevComm.ERR_ALL_TMPL_EMPTY -> "All Templates are Empty"
            DevComm.ERR_INVALID_TMPL_DATA -> "Invalid Template Data"
            DevComm.ERR_DUPLICATION_ID -> "Duplicated ID : "
            DevComm.ERR_BAD_QUALITY -> "Bad Quality Image"
            DevComm.ERR_MERGE_FAIL -> "Merge failed"
            DevComm.ERR_NOT_AUTHORIZED -> "Device not authorized."
            DevComm.ERR_MEMORY -> "Memory Error "
            DevComm.ERR_INVALID_PARAM -> "Invalid Parameter"
            DevComm.ERR_GEN_COUNT -> "Generation Count is invalid"
            DevComm.ERR_INVALID_BUFFER_ID -> "Ram Buffer ID is invalid."
            DevComm.ERR_INVALID_OPERATION_MODE -> "Invalid Operation Mode!"
            DevComm.ERR_FP_NOT_DETECTED -> "Finger is not detected."
            else -> String.format("Fail, error code=%d", nErrorCode)
        }
        return str
    }
    private suspend fun capturing(): Int {
        var w_nRet: Int
        while (true) {
            w_nRet = fingerDevComm.Run_GetImage()
            Log.d(TAG, "capturing: 指纹数据$w_nRet")
            if (w_nRet == DevComm.ERR_CONNECTION) {
//                withContext(Dispatchers.Main){
//                    ToastUtils.showShort("通信异常")
//                }
                Log.d(TAG, "capturing: 通信异常")
                fingerDevComm.Run_SLEDControl(0)
                return -1
            } else if (w_nRet == DevComm.ERR_SUCCESS) break
            if (isCancel) {
                withContext(Dispatchers.Main){
                    ToastUtils.showShort("已取消")
                }
                Log.d(TAG, "capturing: 已取消")
                fingerDevComm.Run_SLEDControl(0)
                return -1
            }
        }
        return 0
    }
}