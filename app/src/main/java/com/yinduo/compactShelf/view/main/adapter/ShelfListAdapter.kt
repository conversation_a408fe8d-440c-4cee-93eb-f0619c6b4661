package com.yinduo.compactShelf.view.main.adapter

import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.yinduo.compactShelf.R
import com.yinduo.compactShelf.base.adapter.BaseBindingAdapter
import com.yinduo.compactShelf.databinding.ItemShelfListBinding
import com.yinduo.compactShelf.utils.DeviceUtil
import com.yinduo.compactShelf.view.main.bean.ShelfBean

class ShelfListAdapter(val onItemClick:(position:Int)->Unit) :BaseBindingAdapter<ShelfBean,ItemShelfListBinding>() {
    private var dividerIndex: Int = -1
    private var showDivider = false
    private var isLeft = false
    override fun getLayoutResId(viewType: Int): Int {
        return R.layout.item_shelf_list
    }

    override fun onBindItem(binding: ItemShelfListBinding?, item: ShelfBean?, position: Int) {
        binding?.apply {
            val bean = getItem(position)
            clRoot.isSelected = bean.isSelected
            ivShelfSelectPoint.isSelected = bean.isSelected
            tvShelfOrder.isSelected = bean.isSelected
            tvShelfOrder.text = bean.order.toString()
            tvShelfName.text = if (bean.isStick) "固定列" else "移动列"
            root.setOnClickListener {
                onItemClick(position)
            }
            val layoutParams = clRoot.layoutParams as RecyclerView.LayoutParams
            if (showDivider && dividerIndex == position){
                if (isLeft){
                    layoutParams.setMargins(DeviceUtil.dip2px(2f),0,DeviceUtil.dip2px(40f),0)
                }else{
                    layoutParams.setMargins(DeviceUtil.dip2px(40f),0,DeviceUtil.dip2px(2f),0)
                }
            }else{
                layoutParams.setMargins(DeviceUtil.dip2px(2f),0,DeviceUtil.dip2px(2f),0)
            }
            clRoot.layoutParams = layoutParams
        }
    }
    fun setShelfDivider(index: Int, showDivider: Boolean, isLeft: Boolean) {
        dividerIndex = index
        this.showDivider = showDivider
        this.isLeft = isLeft
    }
}