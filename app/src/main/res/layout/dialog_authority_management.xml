<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="click"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="734dp"
        android:layout_height="418dp"
        android:background="@mipmap/icon_led_bg">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_back_white_arrow"
            android:layout_marginStart="16dp"
            android:layout_marginTop="17dp"
            android:onClick="@{click}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="用户及权限管理"
            android:textStyle="bold"
            android:textSize="15sp"
            android:textColor="@color/white"
            android:layout_marginTop="@dimen/dp_20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <TextView
            android:id="@+id/tv_permission_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="185dp"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:onClick="@{click}"
            android:text="启动时要验证权限"
            android:textColor="@color/color_00c5ff"
            android:textSize="7sp"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/dp_74"
            app:layout_constraintStart_toStartOf="parent"
            tools:layout_editor_absoluteY="74dp" />
        <TextView
            android:id="@+id/tv_pattern_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="九宫格解锁"
            android:textSize="7sp"
            android:textColor="@color/color_00c5ff"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:layout_marginStart="@dimen/dp_18"
            app:layout_constraintTop_toTopOf="@id/tv_permission_enable"
            app:layout_constraintStart_toEndOf="@+id/tv_permission_enable"/>
        <TextView
            android:id="@+id/tv_finger_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="指纹解锁"
            android:textSize="7sp"
            android:textColor="@color/color_00c5ff"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:layout_marginStart="@dimen/dp_18"
            app:layout_constraintTop_toTopOf="@id/tv_permission_enable"
            app:layout_constraintStart_toEndOf="@+id/tv_pattern_enable"/>
        <TextView
            android:id="@+id/tv_face_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="人脸解锁"
            android:textSize="7sp"
            android:textColor="@color/color_00c5ff"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:layout_marginStart="@dimen/dp_18"
            app:layout_constraintTop_toTopOf="@id/tv_permission_enable"
            app:layout_constraintStart_toEndOf="@+id/tv_finger_enable"/>
        <TextView
            android:id="@+id/tv_login_pic_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="登录拍照"
            android:textSize="7sp"
            android:textColor="@color/color_00c5ff"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:layout_marginStart="@dimen/dp_18"
            app:layout_constraintTop_toTopOf="@id/tv_permission_enable"
            app:layout_constraintStart_toEndOf="@+id/tv_face_enable"/>
        <TextView
            android:visibility="gone"
            android:id="@+id/tv_voiceprint_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{click}"
            android:text="声纹"
            android:textSize="7sp"
            android:textColor="@color/color_00c5ff"
            android:drawableStart="@drawable/selector_check_bg"
            android:drawablePadding="5dp"
            android:layout_marginStart="@dimen/dp_18"
            app:layout_constraintTop_toTopOf="@id/tv_permission_enable"
            app:layout_constraintStart_toEndOf="@+id/tv_login_pic_enable"/>
        <Button
            android:id="@+id/bt_add"
            android:layout_width="@dimen/dp_46"
            android:layout_height="19.48dp"
            android:layout_marginEnd="@dimen/dp_7"
            android:text="新增"
            android:textSize="8sp"
            android:textColor="@color/white"
            android:background="@mipmap/icon_button_bg_hardware_debug"
            android:onClick="@{click}"
            app:layout_constraintTop_toTopOf="@+id/bt_clear"
            app:layout_constraintEnd_toStartOf="@+id/bt_clear"/>

        <Button
            android:id="@+id/bt_clear"
            android:layout_width="@dimen/dp_46"
            android:layout_height="19.48dp"
            android:layout_marginEnd="@dimen/dp_32"
            android:background="@mipmap/icon_button_bg_hardware_debug"
            android:onClick="@{click}"
            android:text="清空"
            android:textColor="@color/white"
            android:textSize="8sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/dp_69" />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_25"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@color/color_008beb"
            android:layout_marginStart="@dimen/dp_32"
            android:layout_marginEnd="@dimen/dp_32"
            app:layout_constraintTop_toBottomOf="@id/bt_clear">

            <include layout="@layout/item_auth_manager"
                android:id="@+id/item_title"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_users"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="280dp"
            app:layout_constraintTop_toBottomOf="@id/cl_title"
            android:layout_marginStart="@dimen/dp_32"
            android:layout_marginEnd="@dimen/dp_32"
            android:layout_marginBottom="@dimen/dp_32"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
